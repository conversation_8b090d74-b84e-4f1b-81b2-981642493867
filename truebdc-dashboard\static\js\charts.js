// Chart.js configuration and initialization

let charts = {};

// TrueBDC brand colors for charts
const chartColors = {
    primary: '#1e40af',
    secondary: '#3b82f6',
    accent: '#06b6d4',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    gradient: ['#1e40af', '#3b82f6', '#06b6d4', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899']
};

// Chart.js default configuration
Chart.defaults.font.family = "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif";
Chart.defaults.color = '#4a5568';
Chart.defaults.plugins.legend.labels.usePointStyle = true;
Chart.defaults.plugins.legend.labels.padding = 20;

function initializeCharts() {
    if (typeof Chart === 'undefined') {
        console.error('Chart.js not loaded');
        return;
    }

    createConversionTrendChart();
    createPerformanceMetricsChart();
}

function createConversionTrendChart() {
    const ctx = document.getElementById('conversionTrendChart');
    if (!ctx) return;

    // Generate sample data for the last 6 months
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    const conversionData = [42, 45, 38, 51, 47, 49];
    const appointmentData = [850, 920, 780, 1050, 980, 1020];

    charts.conversionTrend = new Chart(ctx, {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: 'Conversion Rate (%)',
                data: conversionData,
                borderColor: chartColors.primary,
                backgroundColor: chartColors.primary + '20',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: chartColors.primary,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }, {
                label: 'Total Appointments',
                data: appointmentData,
                borderColor: chartColors.accent,
                backgroundColor: chartColors.accent + '20',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: chartColors.accent,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: chartColors.primary,
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true
                }
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Conversion Rate (%)'
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Total Appointments'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    });
}





function createPerformanceMetricsChart() {
    const ctx = document.getElementById('performanceMetricsChart');
    if (!ctx) return;

    const metrics = ['Lead Conv.', 'Show Rate'];
    const current = [47, 65];
    const target = [50, 70];

    charts.performanceMetrics = new Chart(ctx, {
        type: 'radar',
        data: {
            labels: metrics,
            datasets: [{
                label: 'Current',
                data: current,
                borderColor: chartColors.primary,
                backgroundColor: chartColors.primary + '30',
                borderWidth: 3,
                pointBackgroundColor: chartColors.primary,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }, {
                label: 'Target',
                data: target,
                borderColor: chartColors.success,
                backgroundColor: chartColors.success + '20',
                borderWidth: 3,
                pointBackgroundColor: chartColors.success,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    cornerRadius: 8
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    angleLines: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    pointLabels: {
                        font: {
                            size: 12,
                            weight: '600'
                        }
                    }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
    });
}

// Update charts with real data
function updateChartsWithData(dealerships) {
    if (!dealerships || dealerships.length === 0) return;

    // Update brand performance chart with real data
    updateBrandPerformanceChart(dealerships);
    
    // You can add more real-time data updates here
}

function updateBrandPerformanceChart(dealerships) {
    if (!charts.brandPerformance) return;

    const brandStats = {};
    
    dealerships.forEach(dealership => {
        if (!brandStats[dealership.brand]) {
            brandStats[dealership.brand] = { total: 0, count: 0 };
        }
        
        const conversion = dataManager.calculateConversionRate(
            dealership.internetLeads, 
            dealership.appointmentsSet
        );
        
        if (conversion) {
            brandStats[dealership.brand].total += conversion;
            brandStats[dealership.brand].count += 1;
        }
    });

    const brands = Object.keys(brandStats);
    const averages = brands.map(brand => 
        brandStats[brand].count > 0 
            ? Math.round(brandStats[brand].total / brandStats[brand].count)
            : 0
    );

    charts.brandPerformance.data.labels = brands;
    charts.brandPerformance.data.datasets[0].data = averages;
    charts.brandPerformance.update('active');
}
