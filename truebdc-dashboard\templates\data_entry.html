{% extends "base.html" %}

{% block title %}Data Entry - TrueBDC{% endblock %}

{% block content %}
<div class="modern-data-entry">
    <!-- Header Section -->
    <div class="entry-header">
        <div class="header-content">
            <div class="header-icon">
                <div class="icon-circle">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>
            <div class="header-text">
                <h1 class="header-title">Add New Dealership</h1>
                <p class="header-subtitle">Enter performance data for the current month</p>
            </div>
        </div>
        <div class="header-decoration"></div>
    </div>

    <!-- Main Form Container -->
    <div class="form-wrapper">
        <div class="form-container">
            <!-- Progress Indicator -->
            <div class="progress-indicator">
                <div class="progress-step active" data-step="1">
                    <div class="step-circle">1</div>
                    <span class="step-label">Basic Info</span>
                </div>
                <div class="progress-line"></div>
                <div class="progress-step" data-step="2">
                    <div class="step-circle">2</div>
                    <span class="step-label">Performance</span>
                </div>
                <div class="progress-line"></div>
                <div class="progress-step" data-step="3">
                    <div class="step-circle">3</div>
                    <span class="step-label">Goals</span>
                </div>
            </div>

            <!-- Form Steps -->
            <form id="dealershipForm" class="modern-form">
                <!-- Step 1: Basic Information -->
                <div class="form-step active" data-step="1">
                    <div class="step-header">
                        <h2 class="step-title">Basic Information</h2>
                        <p class="step-description">Let's start with the dealership details</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-field">
                            <label for="dealershipName" class="field-label">
                                <span class="label-text">Dealership Name</span>
                                <span class="label-required">*</span>
                            </label>
                            <div class="input-wrapper">
                                <input type="text" id="dealershipName" name="name" required
                                       minlength="2" maxlength="100"
                                       placeholder="Enter dealership name"
                                       class="form-input">
                                <div class="input-border"></div>
                            </div>
                        </div>

                        <div class="form-field">
                            <label for="crm" class="field-label">
                                <span class="label-text">CRM System</span>
                            </label>
                            <div class="select-wrapper">
                                <select id="crm" name="crm" class="form-select">
                                    <option value="">Select CRM System</option>
                                    <option value="eLeads">eLeads</option>
                                    <option value="VinSolution">VinSolution</option>
                                    <option value="DriveCentric">DriveCentric</option>
                                    <option value="DealerSocket">DealerSocket</option>
                                    <option value="Reynolds & Reynolds">Reynolds & Reynolds</option>
                                </select>
                                <div class="select-arrow">
                                    <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
                                        <path d="M1 1L6 6L11 1" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="form-field">
                            <label for="operatingDays" class="field-label">
                                <span class="label-text">Operating Days</span>
                            </label>
                            <div class="select-wrapper">
                                <select id="operatingDays" name="operatingDays" class="form-select">
                                    <option value="mon-sat">Monday - Saturday</option>
                                    <option value="mon-sun">Monday - Sunday</option>
                                </select>
                                <div class="select-arrow">
                                    <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
                                        <path d="M1 1L6 6L11 1" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Performance Data -->
                <div class="form-step" data-step="2">
                    <div class="step-header">
                        <h2 class="step-title">Performance Data</h2>
                        <p class="step-description">Enter current month performance metrics</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-field">
                            <label for="internetLeads" class="field-label">
                                <span class="label-text">Total Internet Leads</span>
                            </label>
                            <div class="input-wrapper">
                                <input type="number" id="internetLeads" name="internetLeads"
                                       min="0" max="10000" step="1"
                                       placeholder="Enter number of leads"
                                       class="form-input">
                                <div class="input-border"></div>
                            </div>
                            <div class="field-hint">Leave empty if data not available</div>
                        </div>

                        <div class="form-field">
                            <label for="appointmentsSet" class="field-label">
                                <span class="label-text">Total Appointments Set</span>
                                <span class="label-required">*</span>
                            </label>
                            <div class="input-wrapper">
                                <input type="number" id="appointmentsSet" name="appointmentsSet"
                                       min="0" max="10000" step="1" required
                                       placeholder="Enter appointments set"
                                       class="form-input">
                                <div class="input-border"></div>
                            </div>
                        </div>

                        <div class="form-field">
                            <label for="showPercentage" class="field-label">
                                <span class="label-text">Show % According to CRM</span>
                            </label>
                            <div class="input-wrapper">
                                <input type="number" id="showPercentage" name="showPercentage"
                                       min="0" max="100" step="0.1"
                                       placeholder="Enter show percentage"
                                       class="form-input">
                                <div class="input-border"></div>
                                <div class="input-suffix">%</div>
                            </div>
                            <div class="field-hint">Enter as percentage (e.g., 45.5)</div>
                        </div>
                    </div>

                    <!-- Real-time Conversion Preview -->
                    <div class="conversion-preview" id="conversionPreview">
                        <div class="preview-card">
                            <div class="preview-icon">📊</div>
                            <div class="preview-content">
                                <h3 class="preview-title">Calculated Metrics</h3>
                                <div class="preview-metric">
                                    <span class="metric-label">Appointment/Lead Conversion:</span>
                                    <span class="metric-value" id="previewConversion">—</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Goals & Settings -->
                <div class="form-step" data-step="3">
                    <div class="step-header">
                        <h2 class="step-title">Goals & Settings</h2>
                        <p class="step-description">Set targets and additional information</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-field">
                            <label for="monthlyGoal" class="field-label">
                                <span class="label-text">Monthly Goal</span>
                            </label>
                            <div class="input-wrapper">
                                <input type="number" id="monthlyGoal" name="monthlyGoal"
                                       min="0" max="10000" step="1"
                                       placeholder="Enter monthly goal"
                                       class="form-input">
                                <div class="input-border"></div>
                            </div>
                            <div class="field-hint">Target appointments for the month</div>
                        </div>

                        <div class="form-field">
                            <label for="dailyGoal" class="field-label">
                                <span class="label-text">Daily Goal</span>
                            </label>
                            <div class="input-wrapper">
                                <input type="number" id="dailyGoal" name="dailyGoal"
                                       min="0" max="100" step="1"
                                       placeholder="Enter daily goal"
                                       class="form-input">
                                <div class="input-border"></div>
                            </div>
                            <div class="field-hint">Target appointments per day</div>
                        </div>

                        <div class="form-field">
                            <label for="tomorrowAppointments" class="field-label">
                                <span class="label-text">Tomorrow Appointments</span>
                            </label>
                            <div class="input-wrapper">
                                <input type="number" id="tomorrowAppointments" name="tomorrowAppointments"
                                       min="0" max="100" step="1"
                                       placeholder="Enter tomorrow's appointments"
                                       class="form-input">
                                <div class="input-border"></div>
                            </div>
                            <div class="field-hint">Scheduled appointments for next business day</div>
                        </div>

                        <div class="form-field full-width">
                            <label for="notes" class="field-label">
                                <span class="label-text">Notes (Optional)</span>
                            </label>
                            <div class="textarea-wrapper">
                                <textarea id="notes" name="notes" rows="4"
                                          placeholder="Additional notes or comments"
                                          class="form-textarea"></textarea>
                                <div class="textarea-border"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Navigation -->
                <div class="form-navigation">
                    <button type="button" class="nav-btn nav-btn-prev" id="prevBtn" disabled>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M10 12L6 8L10 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Previous
                    </button>

                    <div class="nav-center">
                        <button type="button" class="nav-btn nav-btn-reset" onclick="resetForm()">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M1 4V10C1 11.1046 1.89543 12 3 12H13" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                <path d="M3 4L1 2L3 0" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            Reset
                        </button>
                    </div>

                    <button type="button" class="nav-btn nav-btn-next" id="nextBtn">
                        Next
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>

                    <button type="submit" class="nav-btn nav-btn-submit" id="submitBtn" style="display: none;">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M13.5 3.5L6 11L2.5 7.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Save Dealership
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
        </div>
        <p class="loading-text">Saving dealership data...</p>
    </div>

    <!-- Success/Error Messages -->
    <div class="message-toast" id="messageToast">
        <div class="toast-icon"></div>
        <div class="toast-content">
            <div class="toast-title"></div>
            <div class="toast-message"></div>
        </div>
        <button class="toast-close" onclick="hideMessage()">
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                <path d="M10.5 3.5L3.5 10.5M3.5 3.5L10.5 10.5" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/dateUtils.js') }}"></script>
<script>
    // Modern Form Controller
    class ModernFormController {
        constructor() {
            this.currentStep = 1;
            this.totalSteps = 3;
            this.form = document.getElementById('dealershipForm');
            this.init();
        }

        init() {
            this.setupEventListeners();
            this.updateStepDisplay();
            this.setupRealTimeValidation();
        }

        setupEventListeners() {
            // Navigation buttons
            document.getElementById('nextBtn').addEventListener('click', () => this.nextStep());
            document.getElementById('prevBtn').addEventListener('click', () => this.prevStep());

            // Form submission
            this.form.addEventListener('submit', (e) => this.handleSubmit(e));

            // Real-time conversion calculation
            document.getElementById('internetLeads').addEventListener('input', () => this.updateConversionPreview());
            document.getElementById('appointmentsSet').addEventListener('input', () => this.updateConversionPreview());
        }

        nextStep() {
            if (this.validateCurrentStep() && this.currentStep < this.totalSteps) {
                this.currentStep++;
                this.updateStepDisplay();
                this.animateStepTransition();
            }
        }

        prevStep() {
            if (this.currentStep > 1) {
                this.currentStep--;
                this.updateStepDisplay();
                this.animateStepTransition();
            }
        }

        updateStepDisplay() {
            // Update progress indicator
            document.querySelectorAll('.progress-step').forEach((step, index) => {
                const stepNumber = index + 1;
                if (stepNumber <= this.currentStep) {
                    step.classList.add('active');
                } else {
                    step.classList.remove('active');
                }
                if (stepNumber < this.currentStep) {
                    step.classList.add('completed');
                } else {
                    step.classList.remove('completed');
                }
            });

            // Update form steps
            document.querySelectorAll('.form-step').forEach((step, index) => {
                const stepNumber = index + 1;
                if (stepNumber === this.currentStep) {
                    step.classList.add('active');
                } else {
                    step.classList.remove('active');
                }
            });

            // Update navigation buttons
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const submitBtn = document.getElementById('submitBtn');

            prevBtn.disabled = this.currentStep === 1;

            if (this.currentStep === this.totalSteps) {
                nextBtn.style.display = 'none';
                submitBtn.style.display = 'flex';
            } else {
                nextBtn.style.display = 'flex';
                submitBtn.style.display = 'none';
            }
        }

        animateStepTransition() {
            const activeStep = document.querySelector('.form-step.active');
            if (activeStep) {
                activeStep.style.opacity = '0';
                activeStep.style.transform = 'translateX(20px)';

                setTimeout(() => {
                    activeStep.style.opacity = '1';
                    activeStep.style.transform = 'translateX(0)';
                }, 50);
            }
        }

        validateCurrentStep() {
            const currentStepElement = document.querySelector(`.form-step[data-step="${this.currentStep}"]`);
            const requiredFields = currentStepElement.querySelectorAll('input[required], select[required], textarea[required]');

            let isValid = true;
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    this.showFieldError(field, 'This field is required');
                    isValid = false;
                } else {
                    this.clearFieldError(field);
                }
            });

            return isValid;
        }

        showFieldError(field, message) {
            const wrapper = field.closest('.input-wrapper, .select-wrapper, .textarea-wrapper');
            wrapper.classList.add('error');

            let errorElement = wrapper.querySelector('.field-error');
            if (!errorElement) {
                errorElement = document.createElement('div');
                errorElement.className = 'field-error';
                wrapper.appendChild(errorElement);
            }
            errorElement.textContent = message;
        }

        clearFieldError(field) {
            const wrapper = field.closest('.input-wrapper, .select-wrapper, .textarea-wrapper');
            wrapper.classList.remove('error');
            const errorElement = wrapper.querySelector('.field-error');
            if (errorElement) {
                errorElement.remove();
            }
        }

        setupRealTimeValidation() {
            const inputs = this.form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    if (input.hasAttribute('required') && !input.value.trim()) {
                        this.showFieldError(input, 'This field is required');
                    } else {
                        this.clearFieldError(input);
                    }
                });

                input.addEventListener('input', () => {
                    if (input.classList.contains('error')) {
                        this.clearFieldError(input);
                    }
                });
            });
        }

        updateConversionPreview() {
            const leads = document.getElementById('internetLeads').value;
            const appointments = document.getElementById('appointmentsSet').value;
            const preview = document.getElementById('conversionPreview');

            if (leads && appointments && leads > 0) {
                const conversion = Math.round((appointments / leads) * 100);
                document.getElementById('previewConversion').textContent = conversion + '%';
                preview.style.display = 'block';
                preview.classList.add('show');
            } else {
                preview.classList.remove('show');
                setTimeout(() => {
                    if (!preview.classList.contains('show')) {
                        preview.style.display = 'none';
                    }
                }, 300);
            }
        }

        async handleSubmit(e) {
            e.preventDefault();

            if (!this.validateCurrentStep()) {
                return;
            }

            this.showLoading();

            const formData = new FormData(this.form);
            const data = {};

            // Convert form data to object
            for (let [key, value] of formData.entries()) {
                if (value !== '') {
                    if (['internetLeads', 'appointmentsSet', 'showPercentage', 'monthlyGoal', 'dailyGoal', 'tomorrowAppointments'].includes(key)) {
                        data[key] = value ? parseInt(value) : null;
                    } else {
                        data[key] = value;
                    }
                } else if (['internetLeads', 'showPercentage', 'crm', 'monthlyGoal', 'dailyGoal', 'tomorrowAppointments'].includes(key)) {
                    data[key] = null;
                }
            }

            // Set default status and operating days
            data.status = 'active';
            if (!data.operatingDays) {
                data.operatingDays = 'mon-sat';
            }

            try {
                const response = await fetch('/api/dealerships', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    this.showMessage('Success!', 'Dealership data saved successfully!', 'success');
                    setTimeout(() => {
                        this.resetForm();
                    }, 2000);
                } else {
                    const error = await response.json();
                    this.showMessage('Error', error.message || 'Error saving data', 'error');
                }
            } catch (error) {
                this.showMessage('Warning', 'Network error. Data saved locally.', 'warning');
                // Save to localStorage as fallback
                if (typeof dataManager !== 'undefined') {
                    dataManager.addDealership(data);
                }
                setTimeout(() => {
                    this.resetForm();
                }, 2000);
            } finally {
                this.hideLoading();
            }
        }

        resetForm() {
            this.form.reset();
            this.currentStep = 1;
            this.updateStepDisplay();
            document.getElementById('conversionPreview').style.display = 'none';

            // Clear all errors
            document.querySelectorAll('.field-error').forEach(error => error.remove());
            document.querySelectorAll('.error').forEach(element => element.classList.remove('error'));
        }

        showLoading() {
            document.getElementById('loadingOverlay').classList.add('show');
        }

        hideLoading() {
            document.getElementById('loadingOverlay').classList.remove('show');
        }

        showMessage(title, message, type) {
            const toast = document.getElementById('messageToast');
            const titleElement = toast.querySelector('.toast-title');
            const messageElement = toast.querySelector('.toast-message');
            const iconElement = toast.querySelector('.toast-icon');

            titleElement.textContent = title;
            messageElement.textContent = message;

            // Set icon based on type
            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };
            iconElement.textContent = icons[type] || icons.info;

            toast.className = `message-toast ${type} show`;

            // Auto hide after 5 seconds
            setTimeout(() => {
                this.hideMessage();
            }, 5000);
        }

        hideMessage() {
            const toast = document.getElementById('messageToast');
            toast.classList.remove('show');
        }
    }

    // Initialize the form controller when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
        new ModernFormController();
    });

    // Global functions for backward compatibility
    function resetForm() {
        if (window.formController) {
            window.formController.resetForm();
        }
    }

    function hideMessage() {
        if (window.formController) {
            window.formController.hideMessage();
        }
    }
</script>
{% endblock %}
