* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Primary Colors */
    --primary-50: #f0f4ff;
    --primary-100: #e0edff;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-900: #1e3a8a;

    /* Neutral Grays */
    --neutral-50: #f8fafc;
    --neutral-100: #f1f5f9;
    --neutral-200: #e2e8f0;
    --neutral-300: #cbd5e1;
    --neutral-400: #94a3b8;
    --neutral-500: #64748b;
    --neutral-600: #475569;
    --neutral-700: #334155;
    --neutral-800: #1e293b;
    --neutral-900: #0f172a;

    /* Status Colors */
    --success-50: #f0fdf4;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;

    --warning-50: #fffbeb;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;

    --error-50: #fef2f2;
    --error-500: #ef4444;
    --error-600: #dc2626;
    --error-700: #b91c1c;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #4f8ff7 0%, #2563eb 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-error: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

    /* Border Radius */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;
    --border-radius-3xl: 2rem;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.8) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
        linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: var(--neutral-800);
    line-height: 1.6;
    min-height: 100vh;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Animation Classes */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.4s ease-out;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-count-up {
    animation: countUp 0.8s ease-out;
}

/* Staggered Animation Delays */
.animate-delay-1 { animation-delay: 0.1s; animation-fill-mode: both; }
.animate-delay-2 { animation-delay: 0.2s; animation-fill-mode: both; }
.animate-delay-3 { animation-delay: 0.3s; animation-fill-mode: both; }
.animate-delay-4 { animation-delay: 0.4s; animation-fill-mode: both; }
.animate-delay-5 { animation-delay: 0.5s; animation-fill-mode: both; }

/* Header */
.header {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px) saturate(180%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    position: sticky;
    top: 0;
    z-index: 100;
    padding: 1.5rem 0;
    animation: slideDown 0.8s cubic-bezier(0.23, 1, 0.32, 1);
    box-shadow: var(--shadow-lg);
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Add slide down animation */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Page Load Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes countUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-image,
.logo-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.25rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo-image::before,
.logo-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.logo-image:hover::before,
.logo-icon:hover::before {
    transform: translateX(100%);
}

.logo-image:hover,
.logo-icon:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--neutral-900);
    letter-spacing: -0.025em;
}

.header-stats {
    display: flex;
    gap: 2rem;
    font-size: 0.9rem;
}

.stat-item {
    text-align: center;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-item:hover::before {
    transform: scaleX(1);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: rgba(255, 255, 255, 0.8);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-600);
    font-family: 'JetBrains Mono', monospace;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.75rem;
    color: var(--neutral-600);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Navigation */
.nav {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px) saturate(180%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1.5rem 0;
    position: relative;
    box-shadow: var(--shadow-lg);
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.nav-buttons {
    display: flex;
    gap: 0.75rem;
}

.btn {
    padding: 0.875rem 1.75rem;
    border: none;
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.875rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
    font-family: inherit;
    letter-spacing: 0.025em;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-md);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.btn-primary:hover::before {
    transform: translateX(100%);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.8);
    color: var(--primary-600);
    border: 1px solid rgba(59, 130, 246, 0.2);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--primary-50);
    color: var(--primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
}

.view-toggle {
    display: flex;
    background: #f7fafc;
    border-radius: 8px;
    padding: 4px;
}

.toggle-btn {
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.toggle-btn.active {
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    color: var(--truebdc-primary);
}

.search-filter {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-box {
    padding: 0.875rem 1.25rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius-xl);
    font-size: 0.875rem;
    width: 280px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-family: inherit;
    color: var(--neutral-700);
}

.search-box::placeholder {
    color: var(--neutral-400);
}

.search-box:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.95);
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--shadow-lg);
    transform: translateY(-1px);
}

/* Main Content */
.main-content {
    padding: 2rem 0;
}

/* Table View */
.table-container {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
    position: relative;
}

.table-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.table-title {
    font-size: 1.3rem;
    font-weight: 600;
}

.table-actions {
    display: flex;
    gap: 1rem;
}

.action-btn {
    padding: 0.5rem 1rem;
    background: rgba(255,255,255,0.2);
    border: none;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    font-size: 0.85rem;
    transition: background 0.3s ease;
    text-decoration: none;
}

.action-btn:hover {
    background: rgba(255,255,255,0.3);
}

.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
    table-layout: auto;
}

.data-table th {
    background: #f8fafc;
    padding: 0.75rem 0.75rem;
    text-align: left;
    font-weight: 600;
    color: #64748b;
    border-bottom: 1px solid #e2e8f0;
    font-size: 0.6rem;
    letter-spacing: 0.025em;
    cursor: pointer;
    user-select: none;
    text-transform: uppercase;
    white-space: nowrap;
    min-width: fit-content;
}

.data-table th:hover {
    background: #f1f5f9;
}

.data-table td {
    padding: 0.75rem 0.5rem;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
}

.data-table tbody tr:hover {
    background: #f8fafc;
}

/* Progress Bar Styles */
.progress-container {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 140px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background-color: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-fill.green {
    background-color: #10b981;
}

.progress-fill.yellow {
    background-color: #f59e0b;
}

.progress-fill.red {
    background-color: #ef4444;
}

.progress-text {
    font-size: 0.7rem;
    color: #64748b;
    white-space: nowrap;
    font-weight: 500;
}

/* Circular Progress Styles */
.circular-progress {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.circular-progress.size-sm {
    width: 60px;
    height: 60px;
}

.circular-progress.size-md {
    width: 80px;
    height: 80px;
}

.circular-progress.size-lg {
    width: 100px;
    height: 100px;
}

.circular-progress svg {
    transform: rotate(-90deg);
    width: 100%;
    height: 100%;
}

.circular-progress .progress-ring {
    fill: none;
    stroke-width: 8;
}

.circular-progress .progress-ring.background {
    stroke: #e2e8f0;
}

.circular-progress .progress-ring.foreground {
    stroke-linecap: round;
    transition: stroke-dashoffset 0.5s ease-in-out;
}

/* Color categories */
.circular-progress .progress-ring.poor {
    stroke: #ef4444; /* Red */
}

.circular-progress .progress-ring.below-average {
    stroke: #f97316; /* Orange */
}

.circular-progress .progress-ring.average {
    stroke: #eab308; /* Yellow */
}

.circular-progress .progress-ring.good {
    stroke: #3b82f6; /* Blue */
}

.circular-progress .progress-ring.excellent {
    stroke: #10b981; /* Green */
}

.circular-progress .progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.75rem;
    font-weight: 600;
    color: #374151;
}

.circular-progress.size-sm .progress-text {
    font-size: 0.65rem;
}

.circular-progress.size-lg .progress-text {
    font-size: 0.85rem;
}

/* Animation classes */
.circular-progress.animate-in .progress-ring.foreground {
    animation: progressFill 1s ease-out;
}

.circular-progress.pulse .progress-ring.foreground {
    animation: progressPulse 2s infinite;
}

@keyframes progressFill {
    from {
        stroke-dashoffset: 251.2;
    }
}

@keyframes progressPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.dealership-name {
    font-weight: 600;
    color: #2d3748;
}

.crm-badge {
    display: inline-block;
    padding: 0.375rem 0.875rem;
    border-radius: var(--border-radius-2xl);
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 0.375rem;
    background: rgba(226, 232, 240, 0.8);
    color: var(--neutral-600);
    border: 1px solid rgba(203, 213, 225, 0.5);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    letter-spacing: 0.025em;
}

.crm-badge:hover {
    background: rgba(226, 232, 240, 0.9);
    color: var(--neutral-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.metric-value {
    font-weight: 600;
    font-size: 1.1rem;
}

.percentage {
    padding: 0.625rem 1.25rem;
    border-radius: var(--border-radius-3xl);
    font-weight: 600;
    text-align: center;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.percentage::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.percentage:hover::before {
    transform: translateX(100%);
}

.percentage:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.percentage.excellent {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success-700);
    border-color: rgba(34, 197, 94, 0.2);
}

.percentage.good {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-700);
    border-color: rgba(59, 130, 246, 0.2);
}

.percentage.average {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-700);
    border-color: rgba(245, 158, 11, 0.2);
}

.percentage.poor {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-700);
    border-color: rgba(239, 68, 68, 0.2);
}

.status-active {
    color: #38a169;
    font-weight: 600;
}

.status-inactive {
    color: #e53e3e;
    font-weight: 600;
}

/* Cards View */
.cards-grid {
    display: none;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.cards-grid.active {
    display: grid;
}

.dealership-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-2xl);
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.dealership-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.dealership-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    pointer-events: none;
}

.dealership-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
    background: rgba(255, 255, 255, 0.9);
}

.dealership-card:hover::before {
    transform: scaleX(1);
}

.dealership-card:hover::after {
    transform: translateX(100%);
}

/* Critical status cards */
.dealership-card.critical {
    background: rgba(254, 242, 242, 0.8);
    border-color: rgba(220, 38, 38, 0.2);
}

.dealership-card.critical::before {
    background: var(--gradient-error);
}

.dealership-card.critical:hover {
    background: rgba(254, 242, 242, 0.9);
    box-shadow: 0 25px 50px -12px rgba(220, 38, 38, 0.25);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.card-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.card-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.card-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.metric-box {
    text-align: center;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 8px;
}

.metric-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.25rem;
}

.metric-label {
    font-size: 0.8rem;
    color: #718096;
    font-weight: 500;
}

/* Card Daily Info */
.card-daily-info {
    margin: 1rem 0;
    padding: 0.75rem;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.daily-metric {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.daily-metric:last-child {
    margin-bottom: 0;
}

.daily-icon {
    margin-right: 0.5rem;
    font-size: 1.1em;
}

.daily-label {
    font-weight: 500;
    margin-right: 0.5rem;
    color: #495057;
}

.daily-value {
    font-weight: 600;
    color: #007bff;
}

.card-performance {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 8px;
}

.performance-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

/* Card Insights */
.card-insights {
    padding: 1rem;
    background: linear-gradient(135deg, #e6fffa 0%, #f0fff4 100%);
    border-radius: 8px;
    margin-top: 0.5rem;
}

.insight-section {
    margin-bottom: 0.5rem;
}

.insight-title {
    font-size: 0.85rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.insight-content {
    font-size: 0.8rem;
    color: #4a5568;
    line-height: 1.4;
}

/* Goal Progress Styling Updates */
.progress-detail {
    font-size: 0.75rem;
    color: #718096;
    font-weight: 500;
}

/* Summary Stats */
.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.summary-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.15);
}

.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--truebdc-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.summary-card:hover::before {
    transform: scaleX(1);
}

.summary-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.summary-label {
    color: #718096;
    font-weight: 500;
}

/* Charts Section */
.charts-section {
    margin: 2rem 0;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-container {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(30, 64, 175, 0.1);
    position: relative;
    overflow: hidden;
}

.chart-container:hover {
    transform: translateY(-4px);
    box-shadow: 0 15px 40px rgba(30, 64, 175, 0.15);
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--truebdc-gradient);
}

.chart-header {
    margin-bottom: 1.5rem;
    text-align: center;
}

.chart-header h3 {
    color: var(--truebdc-dark);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
    font-weight: 600;
}

.chart-header p {
    color: #718096;
    font-size: 0.9rem;
}

.chart-container canvas {
    max-height: 300px;
    width: 100% !important;
    height: auto !important;
}

/* Data Entry Styles */
.data-entry-container {
    display: flex;
    justify-content: center;
    max-width: 800px;
    margin: 0 auto;
}

.form-container {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
}

.form-header {
    margin-bottom: 2rem;
    text-align: center;
}

.form-header h2 {
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.form-header p {
    color: #718096;
}

.dealership-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--truebdc-primary);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
    transform: translateY(-1px);
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
    border-color: var(--truebdc-secondary);
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

/* Enhanced Form UX */
.form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-group.has-error input,
.form-group.has-error select,
.form-group.has-error textarea {
    border-color: var(--truebdc-error);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group.has-success input,
.form-group.has-success select,
.form-group.has-success textarea {
    border-color: var(--truebdc-success);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-feedback {
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    font-size: 1.2rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.form-group.has-error .form-feedback {
    color: var(--truebdc-error);
    opacity: 1;
}

.form-group.has-success .form-feedback {
    color: var(--truebdc-success);
    opacity: 1;
}

.form-message {
    margin-top: 0.5rem;
    font-size: 0.85rem;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.form-group.has-error .form-message {
    color: var(--truebdc-error);
    opacity: 1;
    transform: translateY(0);
}

.form-group.has-success .form-message {
    color: var(--truebdc-success);
    opacity: 1;
    transform: translateY(0);
}

/* Floating Labels */
.form-group.floating-label {
    position: relative;
}

.form-group.floating-label label {
    position: absolute;
    top: 50%;
    left: 12px;
    transform: translateY(-50%);
    background: white;
    padding: 0 4px;
    color: #718096;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.form-group.floating-label input:focus + label,
.form-group.floating-label input:not(:placeholder-shown) + label,
.form-group.floating-label select:focus + label,
.form-group.floating-label select:not([value=""]) + label {
    top: 0;
    font-size: 0.75rem;
    color: var(--truebdc-primary);
    font-weight: 600;
}

/* Progress Indicator */
.form-progress {
    width: 100%;
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    margin-bottom: 2rem;
    overflow: hidden;
}

.form-progress-bar {
    height: 100%;
    background: var(--truebdc-gradient);
    width: 0%;
    transition: width 0.5s ease;
    border-radius: 2px;
}

/* Auto-complete Suggestions */
.autocomplete-container {
    position: relative;
}

.autocomplete-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.autocomplete-suggestions.show {
    opacity: 1;
    transform: translateY(0);
}

.autocomplete-suggestion {
    padding: 12px;
    cursor: pointer;
    border-bottom: 1px solid #f7fafc;
    transition: background-color 0.2s ease;
}

.autocomplete-suggestion:hover,
.autocomplete-suggestion.highlighted {
    background: var(--truebdc-light);
    color: var(--truebdc-primary);
}

.autocomplete-suggestion:last-child {
    border-bottom: none;
}

/* Input Loading State */
.form-group.loading input {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none"><circle cx="12" cy="12" r="10" stroke="%233b82f6" stroke-width="2" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416"><animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/><animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/></circle></svg>');
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 20px 20px;
}

.form-group small {
    color: #718096;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1rem;
}

.conversion-preview {
    background: #f7fafc;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.conversion-preview h3 {
    color: #4a5568;
    margin-bottom: 1rem;
}

.preview-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.recent-entries {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    height: fit-content;
}

.recent-entries h3 {
    color: #2d3748;
    margin-bottom: 1rem;
}

.recent-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.no-entries {
    color: #718096;
    text-align: center;
    padding: 2rem;
}

/* Messages */
.message-container {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    z-index: 1000;
    transition: all 0.3s ease;
}

.message-container.success {
    background: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.message-container.error {
    background: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
}

.message-container.warning {
    background: #feebc8;
    color: #c05621;
    border: 1px solid #fbd38d;
}

/* Loading States */
.loading {
    text-align: center;
    color: #718096;
    padding: 2rem;
}

.loading-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    color: #718096;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
    text-align: center;
    z-index: 1000;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid var(--truebdc-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Button Ripple Effect */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:active::after {
    width: 300px;
    height: 300px;
}

.hidden {
    display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .header-stats {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .nav-content {
        flex-direction: column;
        gap: 1rem;
    }

    .search-filter {
        flex-direction: column;
        width: 100%;
    }

    .search-box {
        width: 100%;
    }

    .data-table {
        font-size: 0.85rem;
    }

    .cards-grid {
        grid-template-columns: 1fr;
    }



    .form-row {
        grid-template-columns: 1fr;
    }
}

/* Goal tracking styles */
.goal-progress {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.progress-text {
    font-weight: 600;
    font-size: 0.9em;
}

.progress-percentage {
    font-size: 0.8em;
    opacity: 0.8;
}

.goal-on-track {
    color: var(--success-color);
}

.goal-warning {
    color: var(--warning-color);
}

.goal-behind {
    color: var(--error-color);
}

.goal-behind-row {
    background-color: rgba(239, 68, 68, 0.05);
    border-left: 3px solid var(--error-color);
}

.goal-behind-row:hover {
    background-color: rgba(239, 68, 68, 0.1);
}

.goal-behind-card {
    border-left: 4px solid var(--error-color);
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(255, 255, 255, 1) 100%);
}

.goal-behind-card:hover {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(255, 255, 255, 1) 100%);
}

/* Action buttons */
.action-btn {
    padding: 0.375rem 0.75rem;
    margin: 0 0.25rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 500;
    background: white;
    color: #475569;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
    color: #334155;
}

/* Inline editing styles */
.editable-field {
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: inline-block;
    min-width: 40px;
    text-align: center;
}

.editable-field:hover {
    background-color: rgba(59, 130, 246, 0.1);
    border: 1px dashed var(--truebdc-primary);
}

.inline-edit-input {
    width: 80px;
    padding: 4px 8px;
    border: 2px solid var(--truebdc-primary);
    border-radius: 4px;
    font-size: 0.9em;
    text-align: center;
    background: white;
    outline: none;
}

.inline-edit-input:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Period display styles */
.period-display-container {
    background: white;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

.period-display {
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.period-label {
    font-weight: 600;
    color: var(--truebdc-primary);
    font-size: 0.9em;
}

.period-text {
    font-weight: 700;
    color: #2d3748;
    font-size: 1.1em;
}

.period-note {
    font-size: 0.8em;
    color: #718096;
    font-style: italic;
}

/* Edit Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    margin: 0;
    color: var(--truebdc-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #718096;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #e53e3e;
}

.edit-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.edit-form .form-group {
    display: flex;
    flex-direction: column;
}

.edit-form label {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.edit-form input,
.edit-form select {
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.edit-form input:focus,
.edit-form select:focus {
    outline: none;
    border-color: var(--truebdc-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.date-display {
    background: linear-gradient(135deg, var(--truebdc-primary), var(--truebdc-secondary));
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9em;
}

/* ===== MODERN DATA ENTRY FORM STYLES ===== */

/* Main Container */
.modern-data-entry {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem 1rem;
    position: relative;
    overflow-x: hidden;
}

.modern-data-entry::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    pointer-events: none;
}

/* Header Section */
.entry-header {
    max-width: 1200px;
    margin: 0 auto 3rem;
    position: relative;
    z-index: 2;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.header-icon {
    position: relative;
}

.icon-circle {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-circle:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.header-text {
    flex: 1;
}

.header-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin: 0 0 0.5rem 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.02em;
}

.header-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-weight: 400;
}

.header-decoration {
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    border-radius: 1px;
}

/* Form Wrapper */
.form-wrapper {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.form-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 3rem;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.1),
        0 8px 32px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(120, 119, 198, 0.3) 50%, transparent 100%);
}

/* Progress Indicator */
.progress-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 3rem;
    position: relative;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    z-index: 2;
}

.step-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: #e5e7eb;
    color: #9ca3af;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

.step-circle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.step-circle span {
    position: relative;
    z-index: 1;
}

.progress-step.active .step-circle,
.progress-step.completed .step-circle {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.progress-step.completed .step-circle {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.step-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    transition: color 0.3s ease;
    text-align: center;
}

.progress-step.active .step-label,
.progress-step.completed .step-label {
    color: #374151;
    font-weight: 600;
}

.progress-line {
    width: 120px;
    height: 2px;
    background: #e5e7eb;
    position: relative;
    margin: 0 1rem;
    overflow: hidden;
}

.progress-line::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-step.completed + .progress-line::before {
    width: 100%;
}

/* Form Steps */
.modern-form {
    position: relative;
}

.form-step {
    display: none;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-step.active {
    display: block;
    opacity: 1;
    transform: translateX(0);
}

.step-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.step-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
    letter-spacing: -0.01em;
}

.step-description {
    font-size: 1rem;
    color: #6b7280;
    margin: 0;
    font-weight: 400;
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-field {
    position: relative;
}

.form-field.full-width {
    grid-column: 1 / -1;
}

/* Field Labels */
.field-label {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
    letter-spacing: 0.01em;
}

.label-text {
    flex: 1;
}

.label-required {
    color: #ef4444;
    font-weight: 700;
}

/* Input Wrappers */
.input-wrapper,
.select-wrapper,
.textarea-wrapper {
    position: relative;
    background: white;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-wrapper::before,
.select-wrapper::before,
.textarea-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 2px;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.input-wrapper:focus-within::before,
.select-wrapper:focus-within::before,
.textarea-wrapper:focus-within::before {
    opacity: 1;
}

/* Form Inputs */
.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 400;
    color: #1f2937;
    background: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: transparent;
    box-shadow:
        0 0 0 3px rgba(102, 126, 234, 0.1),
        0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.form-input:hover,
.form-select:hover,
.form-textarea:hover {
    border-color: #d1d5db;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Input Border Animation */
.input-border,
.textarea-border {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
    border-radius: 1px;
}

.input-wrapper:focus-within .input-border,
.textarea-wrapper:focus-within .textarea-border {
    width: 100%;
}

/* Select Styling */
.select-wrapper {
    position: relative;
}

.form-select {
    appearance: none;
    cursor: pointer;
    padding-right: 3rem;
}

.select-arrow {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    pointer-events: none;
    transition: all 0.3s ease;
}

.select-wrapper:focus-within .select-arrow {
    color: #667eea;
    transform: translateY(-50%) rotate(180deg);
}

/* Input Suffix */
.input-suffix {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-weight: 500;
    font-size: 0.9rem;
    pointer-events: none;
}

/* Field Hints */
.field-hint {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: #6b7280;
    font-weight: 400;
}

/* Field Errors */
.field-error {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: #ef4444;
    font-weight: 500;
    opacity: 0;
    transform: translateY(-5px);
    transition: all 0.3s ease;
}

.input-wrapper.error .field-error,
.select-wrapper.error .field-error,
.textarea-wrapper.error .field-error {
    opacity: 1;
    transform: translateY(0);
}

.input-wrapper.error .form-input,
.select-wrapper.error .form-select,
.textarea-wrapper.error .form-textarea {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-wrapper.error::before,
.select-wrapper.error::before,
.textarea-wrapper.error::before {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    opacity: 1;
}

/* Conversion Preview */
.conversion-preview {
    margin-top: 2rem;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: none;
}

.conversion-preview.show {
    opacity: 1;
    transform: translateY(0);
    display: block;
}

.preview-card {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #bae6fd;
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    overflow: hidden;
}

.preview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #0ea5e9 0%, #0284c7 100%);
}

.preview-icon {
    font-size: 1.5rem;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.preview-content {
    flex: 1;
}

.preview-title {
    font-size: 1rem;
    font-weight: 600;
    color: #0c4a6e;
    margin: 0 0 0.5rem 0;
}

.preview-metric {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.metric-label {
    font-size: 0.9rem;
    color: #075985;
    font-weight: 500;
}

.metric-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #0c4a6e;
    background: rgba(255, 255, 255, 0.7);
    padding: 0.25rem 0.75rem;
    border-radius: 8px;
    border: 1px solid rgba(14, 165, 233, 0.2);
}

/* Form Navigation */
.form-navigation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.nav-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    min-width: 120px;
    justify-content: center;
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.nav-btn:hover::before {
    transform: translateX(100%);
}

.nav-btn-prev {
    background: #f3f4f6;
    color: #6b7280;
    border: 2px solid #e5e7eb;
}

.nav-btn-prev:hover:not(:disabled) {
    background: #e5e7eb;
    color: #374151;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-btn-prev:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.nav-btn-next,
.nav-btn-submit {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 2px solid transparent;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.nav-btn-next:hover,
.nav-btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.nav-btn-reset {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 2px solid rgba(239, 68, 68, 0.2);
}

.nav-btn-reset:hover {
    background: rgba(239, 68, 68, 0.15);
    color: #dc2626;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    position: relative;
    width: 80px;
    height: 80px;
    margin-bottom: 1.5rem;
}

.spinner-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1.2s linear infinite;
}

.spinner-ring:nth-child(2) {
    animation-delay: -0.4s;
    border-top-color: #764ba2;
}

.spinner-ring:nth-child(3) {
    animation-delay: -0.8s;
    border-top-color: #667eea;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: white;
    font-size: 1.1rem;
    font-weight: 500;
    text-align: center;
    margin: 0;
}

/* Message Toast */
.message-toast {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    max-width: 400px;
    z-index: 10000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
}

.message-toast.success .toast-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.message-toast.error .toast-icon {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.message-toast.warning .toast-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.message-toast.info .toast-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.25rem 0;
}

.toast-message {
    font-size: 0.9rem;
    color: #6b7280;
    margin: 0;
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.toast-close:hover {
    background: #f3f4f6;
    color: #6b7280;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-data-entry {
        padding: 1rem 0.5rem;
    }

    .header-title {
        font-size: 2rem;
    }

    .header-subtitle {
        font-size: 1rem;
    }

    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .form-container {
        padding: 2rem 1.5rem;
        border-radius: 16px;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .progress-indicator {
        flex-direction: column;
        gap: 1rem;
    }

    .progress-line {
        width: 2px;
        height: 60px;
        margin: 0;
    }

    .progress-line::before {
        width: 100%;
        height: 0%;
    }

    .progress-step.completed + .progress-line::before {
        height: 100%;
    }

    .step-circle {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }

    .step-label {
        font-size: 0.8rem;
    }

    .form-navigation {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-center {
        order: -1;
    }

    .nav-btn {
        width: 100%;
        min-width: auto;
    }

    .message-toast {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .header-title {
        font-size: 1.75rem;
    }

    .icon-circle {
        width: 48px;
        height: 48px;
    }

    .form-container {
        padding: 1.5rem 1rem;
    }

    .step-title {
        font-size: 1.5rem;
    }

    .form-input,
    .form-select,
    .form-textarea {
        padding: 0.875rem 1rem;
        font-size: 0.9rem;
    }
}

/* Animation Enhancements */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.form-container {
    animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-step.active {
    animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-indicator {
    animation: slideInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

/* Focus Management */
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .form-input,
    .form-select,
    .form-textarea {
        border-width: 3px;
    }

    .nav-btn {
        border-width: 3px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .form-step {
        transition: none;
    }

    .progress-step .step-circle {
        transition: none;
    }
}

/* Print Styles */
@media print {
    .modern-data-entry {
        background: white;
        padding: 0;
    }

    .form-container {
        box-shadow: none;
        border: 1px solid #000;
    }

    .nav-btn,
    .loading-overlay,
    .message-toast {
        display: none;
    }
}
