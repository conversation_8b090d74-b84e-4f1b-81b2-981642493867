from flask import Flask, render_template, jsonify, request
import json
from datetime import datetime

app = Flask(__name__)

# Global sample data - in production this would be in a database
# Complete dealership data extracted from the provided table image
# Monthly goals are set based on dealership size and performance expectations
sample_data = [
        {
            "id": 1,
            "name": "All American Chevrolet",
            "crm": "eLeads",
            "internetLeads": None,
            "appointmentsSet": 140,
            "showPercentage": None,
            "monthlyGoal": 180,
            "dailyGoal": 8,
            "tomorrowAppointments": 6,
            "operatingDays": "mon-sat",
            "status": "active"
        },
        {
            "id": 2,
            "name": "<PERSON>",
            "crm": "VinSolution",
            "internetLeads": 778,
            "appointmentsSet": 142,
            "showPercentage": 29,
            "monthlyGoal": 200,
            "dailyGoal": 9,
            "tomorrowAppointments": 7,
            "operatingDays": "mon-sat",
            "status": "active"
        },
        {
            "id": 3,
            "name": "AutoNation Chevrolet Delray",
            "crm": "DriveCentric",
            "internetLeads": 89,
            "appointmentsSet": 67,
            "showPercentage": 75,
            "monthlyGoal": 120,
            "dailyGoal": 5,
            "tomorrowAppointments": 4,
            "operatingDays": "mon-sun",
            "status": "active"
        },
        {
            "id": 4,
            "name": "AutoNation Honda Delray",
            "crm": "DealerSocket",
            "internetLeads": 156,
            "appointmentsSet": 89,
            "showPercentage": 57,
            "monthlyGoal": 150,
            "dailyGoal": 7,
            "tomorrowAppointments": 5,
            "operatingDays": "mon-sat",
            "status": "active"
        },
        {
            "id": 5,
            "name": "AutoNation Subaru Delray",
            "crm": "Reynolds & Reynolds",
            "internetLeads": 67,
            "appointmentsSet": 45,
            "showPercentage": 67,
            "monthlyGoal": 90,
            "dailyGoal": 4,
            "tomorrowAppointments": 3,
            "operatingDays": "mon-sun",
            "status": "active"
        },
        {
            "id": 6,
            "name": "Boca Honda",
            "crm": "eLeads",
            "internetLeads": 234,
            "appointmentsSet": 156,
            "showPercentage": 67,
            "monthlyGoal": 220,
            "dailyGoal": 10,
            "tomorrowAppointments": 8,
            "operatingDays": "mon-sat",
            "status": "active"
        },
        {
            "id": 7,
            "name": "Boca Kia",
            "crm": "VinSolution",
            "internetLeads": 123,
            "appointmentsSet": 89,
            "showPercentage": 72,
            "monthlyGoal": 140,
            "dailyGoal": 6,
            "tomorrowAppointments": 5,
            "operatingDays": "mon-sat",
            "status": "active"
        },
        {
            "id": 8,
            "name": "Boca Subaru",
            "crm": "DriveCentric",
            "internetLeads": 98,
            "appointmentsSet": 67,
            "showPercentage": 68,
            "monthlyGoal": 110,
            "dailyGoal": 5,
            "tomorrowAppointments": 4,
            "operatingDays": "mon-sat",
            "status": "active"
        },
        {
            "id": 9,
            "name": "Boynton Beach Honda",
            "crm": "DealerSocket",
            "internetLeads": 145,
            "appointmentsSet": 98,
            "showPercentage": 68,
            "monthlyGoal": 160,
            "dailyGoal": 7,
            "tomorrowAppointments": 6,
            "operatingDays": "mon-sat",
            "status": "active"
        },
        {
            "id": 10,
            "name": "Braman Honda",
            "crm": "Reynolds & Reynolds",
            "internetLeads": 189,
            "appointmentsSet": 123,
            "showPercentage": 65,
            "monthlyGoal": 190,
            "dailyGoal": 8,
            "tomorrowAppointments": 7,
            "operatingDays": "mon-sun",
            "status": "active"
        },
        {
            "id": 11,
            "name": "Braman Kia",
            "crm": "eLeads",
            "internetLeads": 167,
            "appointmentsSet": 112,
            "showPercentage": 67,
            "monthlyGoal": 170,
            "dailyGoal": 7,
            "tomorrowAppointments": 6,
            "operatingDays": "mon-sat",
            "status": "active"
        },
        {
            "id": 12,
            "name": "Coconut Creek Subaru",
            "crm": "VinSolution",
            "internetLeads": 134,
            "appointmentsSet": 89,
            "showPercentage": 66,
            "monthlyGoal": 140,
            "dailyGoal": 6,
            "tomorrowAppointments": 5,
            "operatingDays": "mon-sat",
            "status": "active"
        },
        {
            "id": 13,
            "name": "Coral Springs Honda",
            "crm": "DriveCentric",
            "internetLeads": 178,
            "appointmentsSet": 134,
            "showPercentage": 75,
            "monthlyGoal": 180,
            "dailyGoal": 8,
            "tomorrowAppointments": 7,
            "operatingDays": "mon-sat",
            "status": "active"
        },
        {
            "id": 14,
            "name": "Coral Springs Kia",
            "crm": "DealerSocket",
            "internetLeads": 156,
            "appointmentsSet": 98,
            "showPercentage": 63,
            "monthlyGoal": 150,
            "dailyGoal": 6,
            "tomorrowAppointments": 5,
            "operatingDays": "mon-sat",
            "status": "active"
        },
        {
            "id": 15,
            "name": "Delray Kia",
            "crm": "Reynolds & Reynolds",
            "internetLeads": 145,
            "appointmentsSet": 87,
            "showPercentage": 60,
            "monthlyGoal": 130,
            "dailyGoal": 5,
            "tomorrowAppointments": 4,
            "operatingDays": "mon-sat",
            "status": "active"
        },
]

@app.route('/')
def dashboard():
    """Main dashboard view"""
    return render_template('dashboard.html')

@app.route('/data-entry')
def data_entry():
    """Data entry form view"""
    return render_template('data_entry.html')

@app.route('/api/dealerships', methods=['GET'])
def get_dealerships():
    """Get all dealerships data with simplified goal calculations"""
    # Add simplified goal progress to each dealership
    enhanced_data = []
    for dealership in sample_data:
        enhanced_dealership = dealership.copy()

        # Calculate simple goal progress
        if dealership.get('appointmentsSet') and dealership.get('monthlyGoal'):
            appointments = dealership['appointmentsSet']
            goal = dealership['monthlyGoal']
            percentage = round((appointments / goal) * 100, 1)

            # Determine status
            if percentage >= 90:
                status = 'on-track'
                status_icon = '🟢'
            elif percentage >= 70:
                status = 'behind'
                status_icon = '🟡'
            else:
                status = 'critical'
                status_icon = '🔴'

            enhanced_dealership['goalProgress'] = {
                'percentage': percentage,
                'status': status,
                'status_icon': status_icon,
                'appointments': appointments,
                'goal': goal,
                'remaining': goal - appointments
            }
        else:
            enhanced_dealership['goalProgress'] = {
                'percentage': 0,
                'status': 'no-data',
                'status_icon': '⚪',
                'appointments': dealership.get('appointmentsSet', 0),
                'goal': dealership.get('monthlyGoal', 0),
                'remaining': 0
            }

        enhanced_data.append(enhanced_dealership)

    return jsonify(enhanced_data)

@app.route('/api/dealerships', methods=['POST'])
def add_dealership():
    """Add new dealership data"""
    data = request.get_json()

    # Validate required fields
    required_fields = ['name', 'appointmentsSet']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'Missing required field: {field}'}), 400

    # Generate new ID (in production this would be handled by the database)
    new_id = max([d['id'] for d in sample_data], default=0) + 1

    # Create new dealership record
    new_dealership = {
        'id': new_id,
        'name': data.get('name'),
        'crm': data.get('crm'),
        'internetLeads': data.get('internetLeads'),
        'appointmentsSet': data.get('appointmentsSet'),
        'showPercentage': data.get('showPercentage'),
        'monthlyGoal': data.get('monthlyGoal'),
        'dailyGoal': data.get('dailyGoal'),
        'tomorrowAppointments': data.get('tomorrowAppointments'),
        'operatingDays': data.get('operatingDays', 'mon-sat'),
        'status': data.get('status', 'active')
    }

    # Add to sample_data (in production this would be saved to database)
    sample_data.append(new_dealership)

    return jsonify({'message': 'Dealership added successfully', 'data': new_dealership}), 201

@app.route('/api/dealerships/<int:dealership_id>', methods=['PUT'])
def update_dealership(dealership_id):
    """Update existing dealership data"""
    data = request.get_json()

    # Validate the data
    allowed_fields = ['name', 'crm', 'internetLeads', 'appointmentsSet', 'monthlyGoal', 'showPercentage',
                     'dailyGoal', 'tomorrowAppointments', 'operatingDays', 'notes']
    update_data = {}

    for field, value in data.items():
        if field in allowed_fields:
            # Convert to appropriate type
            if value is not None:
                try:
                    if field in ['internetLeads', 'appointmentsSet', 'monthlyGoal', 'dailyGoal', 'tomorrowAppointments']:
                        update_data[field] = int(value)
                    elif field == 'showPercentage':
                        update_data[field] = float(value)
                    else:
                        update_data[field] = str(value)
                except (ValueError, TypeError):
                    return jsonify({'error': f'Invalid value for {field}'}), 400
            else:
                update_data[field] = None

    # Here you would update the database
    # For now, simulate updating the in-memory data
    for dealership in sample_data:
        if dealership['id'] == dealership_id:
            dealership.update(update_data)
            break

    return jsonify({'message': f'Dealership {dealership_id} updated successfully', 'data': update_data})

@app.route('/api/dealerships/<int:dealership_id>', methods=['DELETE'])
def delete_dealership(dealership_id):
    """Delete dealership data"""

    # Here you would delete from database
    # For now, just return success
    return jsonify({'message': f'Dealership {dealership_id} deleted successfully'})

@app.route('/api/dealerships/<int:dealership_id>/archive', methods=['PUT'])
def archive_dealership(dealership_id):
    """Archive dealership (mark as inactive)"""

    # Here you would update the database to set status to 'inactive'
    # For now, just return success
    return jsonify({'message': f'Dealership {dealership_id} archived successfully'})

@app.route('/api/stats')
def get_stats():
    """Get dashboard statistics calculated from real data"""

    # Calculate stats from sample_data
    total_leads = 0
    total_appointments = 0
    total_conversions = 0
    total_shows = 0
    dealerships_with_leads = 0
    dealerships_with_shows = 0

    for dealership in sample_data:
        # Count leads and appointments
        if dealership.get('internetLeads'):
            total_leads += dealership['internetLeads']
            dealerships_with_leads += 1
        if dealership.get('appointmentsSet'):
            total_appointments += dealership['appointmentsSet']

        # Calculate conversion rate for this dealership
        if dealership.get('internetLeads') and dealership.get('appointmentsSet'):
            conversion_rate = (dealership['appointmentsSet'] / dealership['internetLeads']) * 100
            total_conversions += conversion_rate

        # Add show percentage
        if dealership.get('showPercentage'):
            total_shows += dealership['showPercentage']
            dealerships_with_shows += 1

    # Calculate averages
    average_conversion = round(total_conversions / dealerships_with_leads) if dealerships_with_leads > 0 else 0
    average_show = round(total_shows / dealerships_with_shows) if dealerships_with_shows > 0 else 0

    stats = {
        'totalLeads': total_leads,
        'totalAppointments': total_appointments,
        'averageConversion': average_conversion,
        'averageShow': average_show,
        'activeStores': len(sample_data),  # All dealerships in sample_data are active
        'totalDealerships': len(sample_data)
    }
    return jsonify(stats)

if __name__ == '__main__':
    app.run(debug=True)
