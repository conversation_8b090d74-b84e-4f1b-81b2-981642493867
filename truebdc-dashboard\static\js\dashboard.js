// Dashboard-specific functionality

// Circular Progress Function
function createCircularProgress(percentage, size = 'md') {
    if (!percentage || percentage === '—' || percentage === 0) {
        return '—';
    }

    const numPercentage = parseInt(percentage);
    if (isNaN(numPercentage)) {
        return '—';
    }

    // Determine color category
    let colorClass;
    if (numPercentage <= 20) {
        colorClass = 'poor';
    } else if (numPercentage <= 40) {
        colorClass = 'below-average';
    } else if (numPercentage <= 60) {
        colorClass = 'average';
    } else if (numPercentage <= 80) {
        colorClass = 'good';
    } else {
        colorClass = 'excellent';
    }

    // Calculate stroke-dasharray and stroke-dashoffset
    // Adjusted radius for smaller circles (30% reduction)
    const radius = size === 'sm' ? 16 : size === 'md' ? 22 : 28;
    const viewBoxSize = size === 'sm' ? 42 : size === 'md' ? 56 : 70;
    const center = viewBoxSize / 2;
    const circumference = 2 * Math.PI * radius;
    const strokeDashoffset = circumference - (numPercentage / 100) * circumference;

    return `
        <div class="circular-progress size-${size} animate-in">
            <svg viewBox="0 0 ${viewBoxSize} ${viewBoxSize}">
                <circle class="progress-ring background" cx="${center}" cy="${center}" r="${radius}"></circle>
                <circle class="progress-ring foreground ${colorClass}"
                        cx="${center}" cy="${center}" r="${radius}"
                        stroke-dasharray="${circumference}"
                        stroke-dashoffset="${strokeDashoffset}"></circle>
            </svg>
            <div class="progress-text">${numPercentage}%</div>
        </div>
    `;
}

function renderPercentageCell(percentage) {
    if (!percentage || percentage === '—') {
        return '—';
    }
    return createCircularProgress(percentage, 'sm');
}

let currentSortField = null;
let currentSortDirection = 'asc';
let currentView = 'table';
let dealershipData = [];

// Current period for display - using DateUtils
const currentPeriod = DateUtils.getCurrentPeriodInfo();

// Simplified goal tracking functions
function calculateSimpleGoalProgress(appointmentsSet, monthlyGoal) {
    if (!appointmentsSet || !monthlyGoal) {
        return {
            percentage: 0,
            status: 'no-data',
            status_icon: '⚪',
            appointments: appointmentsSet || 0,
            goal: monthlyGoal || 0,
            remaining: 0
        };
    }

    const percentage = Math.round((appointmentsSet / monthlyGoal) * 100 * 10) / 10;

    // Determine status
    let status, status_icon;
    if (percentage >= 90) {
        status = 'on-track';
        status_icon = '🟢';
    } else if (percentage >= 70) {
        status = 'behind';
        status_icon = '🟡';
    } else {
        status = 'critical';
        status_icon = '🔴';
    }

    return {
        percentage: percentage,
        status: status,
        status_icon: status_icon,
        appointments: appointmentsSet,
        goal: monthlyGoal,
        remaining: monthlyGoal - appointmentsSet
    };
}

function updatePeriodDisplay() {
    const periodElement = document.getElementById('currentPeriod');
    if (periodElement) {
        periodElement.textContent = currentPeriod.formatted; // Use enhanced format: "August 2025 • Day 4 of 31"
    }
}

/**
 * Format tomorrow appointments for table display
 * @param {number} count - Number of appointments
 * @param {string} operatingDays - Operating days setting
 * @returns {string} Formatted string like "8 (Aug 5)"
 */
function formatTomorrowAppointments(count, operatingDays = 'mon-sat') {
    if (!count) return '—';

    const tomorrowInfo = DateUtils.getTomorrowInfo(operatingDays);
    return `${count} ${tomorrowInfo.tableFormat}`;
}

/**
 * Format tomorrow appointments for card display
 * @param {number} count - Number of appointments
 * @param {string} operatingDays - Operating days setting
 * @returns {string} Formatted string like "8 appointments (Mon, Aug 5)"
 */
function formatTomorrowAppointmentsCard(count, operatingDays = 'mon-sat') {
    if (!count) return '—';

    const tomorrowInfo = DateUtils.getTomorrowInfo(operatingDays);
    return `${count} appointments (${tomorrowInfo.shortDayName}, ${tomorrowInfo.compactWithDate})`;
}

function getGoalStatusClass(progress) {
    switch(progress.status) {
        case 'on-track': return 'goal-on-track';
        case 'behind': return 'goal-warning';
        case 'critical': return 'goal-behind';
        default: return 'goal-no-data';
    }
}

function getGoalInsightMessage(progress) {
    if (progress.status === 'no-data') {
        return 'No goal data available';
    }

    const remaining = progress.remaining;
    const percentage = progress.percentage;

    switch(progress.status) {
        case 'on-track':
            if (percentage >= 100) {
                return `🎉 Goal achieved! ${percentage}% complete`;
            } else {
                return `✅ On track with ${percentage}% complete. Need ${remaining} more appointments`;
            }
        case 'behind':
            return `⚠️ Behind pace at ${percentage}%. Need ${remaining} more appointments to reach goal`;
        case 'critical':
            return `🚨 Critical: Only ${percentage}% complete. Need ${remaining} more appointments`;
        default:
            return `Progress: ${percentage}%`;
    }
}



function setupViewToggle() {
    const toggleBtns = document.querySelectorAll('.toggle-btn');
    const tableView = document.getElementById('tableView');
    const cardsView = document.getElementById('cardsView');

    toggleBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            toggleBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');

            currentView = btn.dataset.view;
            if (currentView === 'table') {
                tableView.style.display = 'block';
                cardsView.classList.remove('active');
            } else {
                tableView.style.display = 'none';
                cardsView.classList.add('active');
            }
        });
    });
}

function setupSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce((e) => {
            const searchTerm = e.target.value;
            filterAndRenderData(searchTerm);
        }, 300));
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

async function loadDashboardData() {
    showLoading(true);
    
    try {
        // Try to fetch from API first
        const response = await fetch('/api/dealerships');
        if (response.ok) {
            dealershipData = await response.json();
        } else {
            // Fallback to localStorage
            dealershipData = dataManager.getAllDealerships();
        }

        // Sort data by name (all dealerships are active)
        dealershipData.sort((a, b) => a.name.localeCompare(b.name));

        renderTable(dealershipData);
        renderCards(dealershipData);

        // Update charts with real data
        if (typeof updateChartsWithData === 'function') {
            updateChartsWithData(dealershipData);
        }

    } catch (error) {
        console.error('Error loading dashboard data:', error);
        // Fallback to localStorage
        dealershipData = dataManager.getAllDealerships();

        // Sort data by name (all dealerships are active)
        dealershipData.sort((a, b) => a.name.localeCompare(b.name));

        renderTable(dealershipData);
        renderCards(dealershipData);

        // Update charts with fallback data
        if (typeof updateChartsWithData === 'function') {
            updateChartsWithData(dealershipData);
        }

        showMessage('Using offline data', 'warning');
    } finally {
        showLoading(false);
    }
}

async function loadSummaryStats() {
    try {
        const response = await fetch('/api/stats');
        if (response.ok) {
            const stats = await response.json();
            updateSummaryDisplay(stats);
        } else {
            const stats = calculateStatsFromData(dealershipData);
            updateSummaryDisplay(stats);
        }
    } catch (error) {
        console.error('Error loading summary stats:', error);
        const stats = calculateStatsFromData(dealershipData);
        updateSummaryDisplay(stats);
    }
}

function calculateStatsFromData(data) {
    if (!data || data.length === 0) {
        return {
            totalDealerships: 0,
            activeStores: 0,
            averageConversion: 0,
            averageShow: 0,
            totalLeads: 0,
            totalAppointments: 0
        };
    }

    let totalLeads = 0;
    let totalAppointments = 0;
    let totalConversions = 0;
    let totalShows = 0;
    let dealershipsWithLeads = 0;
    let dealershipsWithShows = 0;

    data.forEach(dealership => {
        // Count leads and appointments
        if (dealership.internetLeads) {
            totalLeads += dealership.internetLeads;
            dealershipsWithLeads++;
        }
        if (dealership.appointmentsSet) {
            totalAppointments += dealership.appointmentsSet;
        }

        // Calculate conversion rate for this dealership
        if (dealership.internetLeads && dealership.appointmentsSet) {
            const conversionRate = (dealership.appointmentsSet / dealership.internetLeads) * 100;
            totalConversions += conversionRate;
        }

        // Add show percentage
        if (dealership.showPercentage) {
            totalShows += dealership.showPercentage;
            dealershipsWithShows++;
        }
    });

    return {
        totalDealerships: data.length,
        activeStores: data.length, // All dealerships in the data are active
        averageConversion: dealershipsWithLeads > 0 ? Math.round(totalConversions / dealershipsWithLeads) : 0,
        averageShow: dealershipsWithShows > 0 ? Math.round(totalShows / dealershipsWithShows) : 0,
        totalLeads: totalLeads,
        totalAppointments: totalAppointments
    };
}

function updateSummaryDisplay(stats) {
    // Update dashboard summary cards
    const summaryElements = {
        summaryTotalLeads: document.getElementById('summaryTotalLeads'),
        summaryTotalAppointments: document.getElementById('summaryTotalAppointments'),
        summaryAvgConversion: document.getElementById('summaryAvgConversion'),
        summaryActiveStores: document.getElementById('summaryActiveStores')
    };

    if (summaryElements.summaryTotalLeads) {
        summaryElements.summaryTotalLeads.textContent = stats.totalLeads ? stats.totalLeads.toLocaleString() : '—';
    }
    if (summaryElements.summaryTotalAppointments) {
        summaryElements.summaryTotalAppointments.textContent = stats.totalAppointments ? stats.totalAppointments.toLocaleString() : '—';
    }
    if (summaryElements.summaryAvgConversion) {
        summaryElements.summaryAvgConversion.textContent = stats.averageConversion ? stats.averageConversion + '%' : '—';
    }
    if (summaryElements.summaryActiveStores) {
        summaryElements.summaryActiveStores.textContent = stats.activeStores || '—';
    }

    // Also update header stats (from base.html)
    const headerElements = {
        totalLeads: document.getElementById('totalLeads'),
        totalAppointments: document.getElementById('totalAppointments'),
        averageConversion: document.getElementById('averageConversion'),
        activeStores: document.getElementById('activeStores')
    };

    if (headerElements.totalLeads) {
        headerElements.totalLeads.textContent = stats.totalLeads ? stats.totalLeads.toLocaleString() : '—';
    }
    if (headerElements.totalAppointments) {
        headerElements.totalAppointments.textContent = stats.totalAppointments ? stats.totalAppointments.toLocaleString() : '—';
    }
    if (headerElements.averageConversion) {
        headerElements.averageConversion.textContent = stats.averageConversion ? stats.averageConversion + '%' : '—';
    }
    if (headerElements.activeStores) {
        headerElements.activeStores.textContent = stats.activeStores || '—';
    }
}

function renderTable(data) {
    const tbody = document.getElementById('tableBody');
    if (!tbody) return;
    
    tbody.innerHTML = '';

    if (data.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="loading">No dealerships found</td></tr>';
        return;
    }

    data.forEach(dealership => {
        const conversionRate = dataManager.calculateConversionRate(
            dealership.internetLeads,
            dealership.appointmentsSet
        );

        // Use goalProgress from API if available, otherwise calculate locally
        const goalProgress = dealership.goalProgress ||
            calculateSimpleGoalProgress(dealership.appointmentsSet, dealership.monthlyGoal);
        const goalStatusClass = getGoalStatusClass(goalProgress);

        const row = document.createElement('tr');
        row.className = goalProgress.status === 'critical' ? 'goal-behind-row' : '';
        row.innerHTML = `
            <td>
                <div class="dealership-name">${dealership.name}</div>
                <div class="crm-badge">${dealership.crm || '—'}</div>
            </td>
            <td>
                <span class="editable-field"
                      data-field="internetLeads"
                      data-id="${dealership.id}"
                      onclick="makeEditable(this)">${dealership.internetLeads || '—'}</span>
            </td>
            <td>
                <span class="editable-field"
                      data-field="appointmentsSet"
                      data-id="${dealership.id}"
                      onclick="makeEditable(this)">${dealership.appointmentsSet || '—'}</span>
            </td>
            <td>
                <span class="editable-field"
                      data-field="dailyGoal"
                      data-id="${dealership.id}"
                      onclick="makeEditable(this)">${dealership.dailyGoal || '—'}</span>
            </td>
            <td>
                <span class="editable-field tomorrow-appointments"
                      data-field="tomorrowAppointments"
                      data-id="${dealership.id}"
                      data-operating-days="${dealership.operatingDays || 'mon-sat'}"
                      onclick="makeEditable(this)">${formatTomorrowAppointments(dealership.tomorrowAppointments, dealership.operatingDays)}</span>
            </td>
            <td>
                <span class="editable-field"
                      data-field="monthlyGoal"
                      data-id="${dealership.id}"
                      onclick="makeEditable(this)">${dealership.monthlyGoal || '—'}</span>
            </td>
            <td>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill ${goalProgress.status === 'on-track' ? 'green' : goalProgress.status === 'behind' ? 'yellow' : 'red'}"
                             style="width: ${goalProgress.percentage}%"></div>
                    </div>
                    <div class="progress-text">${goalProgress.percentage}% (${goalProgress.appointments}/${goalProgress.goal})</div>
                </div>
            </td>
            <td>
                ${conversionRate ?
                    renderPercentageCell(conversionRate) :
                    '—'
                }
            </td>
            <td>
                ${dealership.showPercentage ?
                    renderPercentageCell(dealership.showPercentage) :
                    '—'
                }
            </td>
            <td>
                <button class="action-btn" onclick="editDealership(${dealership.id})" title="Edit">Edit</button>
                <button class="action-btn" onclick="viewDealershipDetails(${dealership.id})" title="View">View</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function renderCards(data) {
    const cardsContainer = document.getElementById('cardsView');
    if (!cardsContainer) return;

    cardsContainer.innerHTML = '';

    if (data.length === 0) {
        cardsContainer.innerHTML = '<div class="loading-card">No dealerships found</div>';
        return;
    }

    data.forEach((dealership, index) => {
        const conversionRate = dataManager.calculateConversionRate(
            dealership.internetLeads,
            dealership.appointmentsSet
        );

        // Use goalProgress from API if available, otherwise calculate locally
        const goalProgress = dealership.goalProgress ||
            calculateSimpleGoalProgress(dealership.appointmentsSet, dealership.monthlyGoal);
        const goalStatusClass = getGoalStatusClass(goalProgress);

        const card = document.createElement('div');
        card.className = `dealership-card ${goalProgress.status === 'critical' ? 'critical' : ''}`;
        card.onclick = () => viewDealershipDetails(dealership.id);

        // Add staggered animation delay
        card.style.animationDelay = `${index * 0.1}s`;

        card.innerHTML = `
            <div class="card-header">
                <div>
                    <div class="card-title">${dealership.name}</div>
                    <div class="crm-badge">${dealership.crm || '—'}</div>
                </div>
                <div class="goal-progress ${goalStatusClass}">
                    <div class="progress-text">${goalProgress.status_icon} ${goalProgress.percentage}%</div>
                    <div class="progress-detail">${goalProgress.appointments}/${goalProgress.goal}</div>
                </div>
            </div>

            <div class="card-metrics">
                <div class="metric-box">
                    <div class="metric-number">${dealership.internetLeads || '—'}</div>
                    <div class="metric-label">Internet Leads</div>
                </div>
                <div class="metric-box">
                    <div class="metric-number">${dealership.appointmentsSet || '—'}</div>
                    <div class="metric-label">Appointments</div>
                </div>
                <div class="metric-box">
                    <div class="metric-number">${dealership.monthlyGoal || '—'}</div>
                    <div class="metric-label">Monthly Goal</div>
                </div>
            </div>

            <div class="card-daily-info">
                <div class="daily-metric">
                    <span class="daily-icon">🎯</span>
                    <span class="daily-label">Daily Goal:</span>
                    <span class="daily-value">${dealership.dailyGoal || '—'}</span>
                </div>
                <div class="daily-metric">
                    <span class="daily-icon">📅</span>
                    <span class="daily-label">Tomorrow:</span>
                    <span class="daily-value">${formatTomorrowAppointmentsCard(dealership.tomorrowAppointments, dealership.operatingDays)}</span>
                </div>
            </div>

            <div class="card-performance">
                <div class="performance-indicator">
                    <span>Lead Conversion:</span>
                    <div class="performance-value">
                        ${conversionRate ?
                            createCircularProgress(conversionRate, 'sm') :
                            '<span class="percentage">—</span>'
                        }
                    </div>
                </div>
                <div class="performance-indicator">
                    <span>Show Rate:</span>
                    <div class="performance-value">
                        ${dealership.showPercentage ?
                            createCircularProgress(dealership.showPercentage, 'sm') :
                            '<span class="percentage">—</span>'
                        }
                    </div>
                </div>
            </div>

            <div class="card-insights">
                <div class="insight-section">
                    <div class="insight-title">📊 Goal Status</div>
                    <div class="insight-content">
                        ${getGoalInsightMessage(goalProgress)}
                    </div>
                </div>
            </div>
        `;

        cardsContainer.appendChild(card);
    });
}

function sortTable(columnIndex) {
    const columns = ['name', 'internetLeads', 'appointmentsSet', 'monthlyGoal', 'goalProgress', 'conversionRate', 'showPercentage'];
    const field = columns[columnIndex];

    if (currentSortField === field) {
        currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        currentSortField = field;
        currentSortDirection = 'asc';
    }

    const sortedData = dataManager.sortDealerships(field, currentSortDirection);
    renderTable(sortedData);
    renderCards(sortedData);
}

function filterAndRenderData(searchTerm) {
    let filteredData = dealershipData;

    if (searchTerm) {
        filteredData = dataManager.searchDealerships(searchTerm);
    }

    renderTable(filteredData);
    renderCards(filteredData);
}

function editDealership(id) {
    // Find the dealership data
    const dealership = dealershipData.find(d => d.id === id);
    if (!dealership) {
        console.error('Dealership not found:', id);
        return;
    }

    // Populate the edit form
    document.getElementById('editId').value = dealership.id;
    document.getElementById('editName').value = dealership.name || '';
    document.getElementById('editCrm').value = dealership.crm || '';
    document.getElementById('editInternetLeads').value = dealership.internetLeads || '';
    document.getElementById('editAppointmentsSet').value = dealership.appointmentsSet || '';
    document.getElementById('editShowPercentage').value = dealership.showPercentage || '';
    document.getElementById('editMonthlyGoal').value = dealership.monthlyGoal || '';
    document.getElementById('editDailyGoal').value = dealership.dailyGoal || '';
    document.getElementById('editTomorrowAppointments').value = dealership.tomorrowAppointments || '';
    document.getElementById('editOperatingDays').value = dealership.operatingDays || 'mon-sat';
    document.getElementById('editNotes').value = dealership.notes || '';

    // Show the modal
    document.getElementById('editModal').classList.remove('hidden');
}

function closeEditModal() {
    document.getElementById('editModal').classList.add('hidden');
}

// Handle edit form submission
document.addEventListener('DOMContentLoaded', function() {
    const editForm = document.getElementById('editForm');
    if (editForm) {
        editForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = {};

            // Convert form data to object
            for (let [key, value] of formData.entries()) {
                if (key === 'id') {
                    data[key] = parseInt(value);
                } else if (['internetLeads', 'appointmentsSet', 'showPercentage', 'monthlyGoal', 'dailyGoal', 'tomorrowAppointments'].includes(key)) {
                    data[key] = value ? parseInt(value) : null;
                } else {
                    data[key] = value || null;
                }
            }

            try {
                const response = await fetch(`/api/dealerships/${data.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    closeEditModal();
                    loadDashboardData(); // Refresh the data
                    showNotification('Dealership updated successfully!', 'success');
                } else {
                    const error = await response.json();
                    showNotification(error.message || 'Error updating dealership', 'error');
                }
            } catch (error) {
                console.error('Error updating dealership:', error);
                showNotification('Network error. Please try again.', 'error');
            }
        });
    }
});

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        z-index: 1001;
        background: ${type === 'success' ? '#10b981' : '#ef4444'};
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    `;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

async function deleteDealership(id) {
    if (!confirm('Are you sure you want to delete this dealership?')) {
        return;
    }

    try {
        const response = await fetch(`/api/dealerships/${id}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            showMessage('Dealership deleted successfully!', 'success');
            await loadDashboardData();
            await loadHeaderStats();
        } else {
            const error = await response.json();
            showMessage(error.message || 'Error deleting dealership', 'error');
        }
    } catch (error) {
        console.error('Delete error:', error);
        // Fallback to localStorage
        dataManager.deleteDealership(id);
        showMessage('Dealership deleted locally', 'warning');
        await loadDashboardData();
        await loadHeaderStats();
    }
}

async function archiveDealership(id) {
    if (!confirm('Are you sure you want to archive this dealership? It will be marked as inactive.')) {
        return;
    }

    try {
        const response = await fetch(`/api/dealerships/${id}/archive`, {
            method: 'PUT'
        });

        if (response.ok) {
            showMessage('Dealership archived successfully!', 'success');
            await loadDashboardData();
            await loadHeaderStats();
        } else {
            const error = await response.json();
            showMessage(error.message || 'Error archiving dealership', 'error');
        }
    } catch (error) {
        console.error('Archive error:', error);
        showMessage('Error archiving dealership', 'error');
    }
}

function viewDealershipDetails(id) {
    // For now, just log the ID
    // You could implement a modal or detail view
    console.log('View dealership details:', id);
    editDealership(id);
}

// Inline editing functions
function makeEditable(element) {
    const currentValue = element.textContent === '—' ? '' : element.textContent;
    const field = element.dataset.field;
    const id = element.dataset.id;

    // Create input element
    const input = document.createElement('input');
    input.type = 'number';
    input.value = currentValue;
    input.className = 'inline-edit-input';
    input.min = '0';

    // Handle save on Enter or blur
    const saveValue = async () => {
        const newValue = input.value.trim();
        const numericValue = newValue === '' ? null : parseInt(newValue);

        try {
            await updateDealershipField(id, field, numericValue);
            element.textContent = newValue === '' ? '—' : newValue;

            // Refresh the data to update calculations
            await loadDashboardData();
            showMessage('Field updated successfully!', 'success');
        } catch (error) {
            console.error('Error updating field:', error);
            element.textContent = currentValue === '' ? '—' : currentValue;
            showMessage('Error updating field', 'error');
        }
    };

    const cancelEdit = () => {
        element.textContent = currentValue === '' ? '—' : currentValue;
    };

    input.addEventListener('blur', saveValue);
    input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            saveValue();
        } else if (e.key === 'Escape') {
            cancelEdit();
        }
    });

    // Replace element content with input
    element.textContent = '';
    element.appendChild(input);
    input.focus();
    input.select();
}

async function updateDealershipField(id, field, value) {
    const updateData = {};
    updateData[field] = value;

    try {
        const response = await fetch(`/api/dealerships/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
        });

        if (!response.ok) {
            throw new Error('Failed to update dealership');
        }

        return await response.json();
    } catch (error) {
        console.error('Update error:', error);
        throw error;
    }
}

// Dashboard initialization function (called by main.js)
function initializeDashboard() {
    updatePeriodDisplay();
    loadDashboardData();
    loadSummaryStats();
    setupViewToggle();
    setupSearch();

    // Initialize charts after a short delay to ensure DOM is ready
    setTimeout(() => {
        if (typeof initializeCharts === 'function') {
            initializeCharts();
        }
    }, 100);
}
