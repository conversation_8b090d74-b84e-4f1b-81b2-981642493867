# TrueBDC Flask Application Files

## File Structure
```
truebdc-dashboard/
├── app.py
├── requirements.txt
├── templates/
│   ├── base.html
│   ├── dashboard.html
│   └── data_entry.html
└── static/
    ├── css/
    │   └── main.css
    └── js/
        ├── main.js
        ├── dashboard.js
        └── dataManager.js
```

---

## 1. app.py
```python
from flask import Flask, render_template, jsonify, request
import json
from datetime import datetime

app = Flask(__name__)

@app.route('/')
def dashboard():
    """Main dashboard view"""
    return render_template('dashboard.html')

@app.route('/data-entry')
def data_entry():
    """Data entry form view"""
    return render_template('data_entry.html')

@app.route('/api/dealerships', methods=['GET'])
def get_dealerships():
    """Get all dealerships data"""
    # This will eventually connect to your database
    # For now, returning sample data
    sample_data = [
        {
            "id": 1,
            "name": "All American Chevrolet",
            "brand": "Chevrolet",
            "internetLeads": None,
            "appointmentsSet": 140,
            "showPercentage": None,
            "status": "active"
        },
        {
            "id": 2,
            "name": "Archer Kia",
            "brand": "Kia",
            "internetLeads": 778,
            "appointmentsSet": 142,
            "showPercentage": 29,
            "status": "active"
        },
        # Add more sample data as needed
    ]
    return jsonify(sample_data)

@app.route('/api/dealerships', methods=['POST'])
def add_dealership():
    """Add new dealership data"""
    data = request.get_json()
    
    # Validate required fields
    required_fields = ['name', 'brand', 'appointmentsSet', 'status']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'Missing required field: {field}'}), 400
    
    # Here you would save to your database
    # For now, just return success
    return jsonify({'message': 'Dealership added successfully', 'data': data}), 201

@app.route('/api/dealerships/<int:dealership_id>', methods=['PUT'])
def update_dealership(dealership_id):
    """Update existing dealership data"""
    data = request.get_json()
    
    # Here you would update the database
    # For now, just return success
    return jsonify({'message': f'Dealership {dealership_id} updated successfully', 'data': data})

@app.route('/api/dealerships/<int:dealership_id>', methods=['DELETE'])
def delete_dealership(dealership_id):
    """Delete dealership data"""
    
    # Here you would delete from database
    # For now, just return success
    return jsonify({'message': f'Dealership {dealership_id} deleted successfully'})

@app.route('/api/stats')
def get_stats():
    """Get dashboard statistics"""
    # Calculate stats from your data
    stats = {
        'totalLeads': 12458,
        'totalAppointments': 5847,
        'averageConversion': 47,
        'activeStores': 18,
        'totalDealerships': 23
    }
    return jsonify(stats)

if __name__ == '__main__':
    app.run(debug=True)
```

---

## 2. requirements.txt
```
Flask==2.3.3
Werkzeug==2.3.7
```

---

## 3. templates/base.html
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TrueBDC Dashboard{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">📊</div>
                    TrueBDC Dashboard
                </div>
                <div class="header-stats">
                    <div class="stat-item">
                        <div class="stat-value" id="totalLeads">—</div>
                        <div class="stat-label">Total Leads</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="totalAppointments">—</div>
                        <div class="stat-label">Appointments</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="averageConversion">—</div>
                        <div class="stat-label">Avg Conversion</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="activeStores">—</div>
                        <div class="stat-label">Active Stores</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="nav">
        <div class="container">
            <div class="nav-content">
                <div class="nav-buttons">
                    <a href="{{ url_for('dashboard') }}" class="btn btn-primary">📊 Dashboard</a>
                    <a href="{{ url_for('data_entry') }}" class="btn btn-secondary">📝 Data Entry</a>
                    <button class="btn btn-secondary">📈 Reports</button>
                    <button class="btn btn-secondary">⚙️ Settings</button>
                </div>
                
                {% block navigation_extra %}{% endblock %}
            </div>
        </div>
    </div>

    <div class="container">
        <div class="main-content">
            {% block content %}{% endblock %}
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/dataManager.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
```

---

## 4. templates/dashboard.html
```html
{% extends "base.html" %}

{% block title %}Dashboard - TrueBDC{% endblock %}

{% block navigation_extra %}
<div class="search-filter">
    <input type="text" class="search-box" placeholder="Search dealerships..." id="searchInput">
    <div class="view-toggle">
        <button class="toggle-btn active" data-view="table">📋 Table</button>
        <button class="toggle-btn" data-view="cards">🏢 Cards</button>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Summary Statistics -->
<div class="summary-stats">
    <div class="summary-card">
        <div class="summary-number" id="summaryTotalDealerships">—</div>
        <div class="summary-label">Total Dealerships</div>
    </div>
    <div class="summary-card">
        <div class="summary-number" id="summaryActiveStores">—</div>
        <div class="summary-label">Active Stores</div>
    </div>
    <div class="summary-card">
        <div class="summary-number" id="summaryAvgConversion">—</div>
        <div class="summary-label">Avg Lead Conversion</div>
    </div>
    <div class="summary-card">
        <div class="summary-number" id="summaryAvgShow">—</div>
        <div class="summary-label">Avg Show Rate</div>
    </div>
</div>

<!-- Table View -->
<div class="table-container" id="tableView">
    <div class="table-header">
        <div class="table-title">📊 Dealership Performance - Month to Date</div>
        <div class="table-actions">
            <button class="action-btn" onclick="exportData()">📤 Export</button>
            <button class="action-btn" onclick="refreshData()">🔄 Refresh</button>
            <a href="{{ url_for('data_entry') }}" class="action-btn">📝 Add Entry</a>
        </div>
    </div>
    <table class="data-table">
        <thead>
            <tr>
                <th onclick="sortTable(0)">Dealership ↕</th>
                <th onclick="sortTable(1)">Total Internet Leads ↕</th>
                <th onclick="sortTable(2)">Total Appointments Set ↕</th>
                <th onclick="sortTable(3)">Appointment/Lead % ↕</th>
                <th onclick="sortTable(4)">Show % According to CRM ↕</th>
                <th onclick="sortTable(5)">Status ↕</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody id="tableBody">
            <tr>
                <td colspan="7" class="loading">Loading data...</td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Cards View -->
<div class="cards-grid" id="cardsView">
    <div class="loading-card">Loading dealerships...</div>
</div>

<!-- Loading indicator -->
<div id="loadingIndicator" class="loading-indicator hidden">
    <div class="spinner"></div>
    <p>Loading data...</p>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
{% endblock %}
```

---

## 5. templates/data_entry.html
```html
{% extends "base.html" %}

{% block title %}Data Entry - TrueBDC{% endblock %}

{% block content %}
<div class="data-entry-container">
    <div class="form-container">
        <div class="form-header">
            <h2>📝 Add New Dealership Data</h2>
            <p>Enter dealership performance data for the current month</p>
        </div>
        
        <form id="dealershipForm" class="dealership-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="dealershipName">Dealership Name *</label>
                    <input type="text" id="dealershipName" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="brand">Brand *</label>
                    <select id="brand" name="brand" required>
                        <option value="">Select Brand</option>
                        <option value="Chevrolet">Chevrolet</option>
                        <option value="Kia">Kia</option>
                        <option value="Honda">Honda</option>
                        <option value="Toyota">Toyota</option>
                        <option value="Mitsubishi">Mitsubishi</option>
                        <option value="Subaru">Subaru</option>
                        <option value="Nissan">Nissan</option>
                        <option value="Buick">Buick</option>
                        <option value="GMC">GMC</option>
                    </select>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="internetLeads">Total Internet Leads</label>
                    <input type="number" id="internetLeads" name="internetLeads" min="0">
                    <small>Leave empty if data not available</small>
                </div>
                
                <div class="form-group">
                    <label for="appointmentsSet">Total Appointments Set *</label>
                    <input type="number" id="appointmentsSet" name="appointmentsSet" min="0" required>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="showPercentage">Show % According to CRM</label>
                    <input type="number" id="showPercentage" name="showPercentage" min="0" max="100" step="0.1">
                    <small>Enter as percentage (e.g., 45.5)</small>
                </div>
                
                <div class="form-group">
                    <label for="status">Status *</label>
                    <select id="status" name="status" required>
                        <option value="">Select Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group full-width">
                    <label for="notes">Notes (Optional)</label>
                    <textarea id="notes" name="notes" rows="3" placeholder="Additional notes or comments"></textarea>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="resetForm()">🔄 Reset</button>
                <button type="submit" class="btn btn-primary">💾 Save Dealership</button>
            </div>
        </form>
        
        <!-- Conversion Rate Preview -->
        <div class="conversion-preview" id="conversionPreview" style="display: none;">
            <h3>📊 Calculated Metrics</h3>
            <div class="preview-stats">
                <div class="preview-stat">
                    <span class="preview-label">Appointment/Lead Conversion:</span>
                    <span class="preview-value" id="previewConversion">—</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Entries -->
    <div class="recent-entries">
        <h3>📋 Recent Entries</h3>
        <div id="recentEntriesList" class="recent-list">
            <p class="no-entries">No recent entries</p>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<div id="messageContainer" class="message-container hidden"></div>
{% endblock %}

{% block extra_js %}
<script>
    // Form handling and validation
    document.getElementById('dealershipForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = {};
        
        // Convert form data to object
        for (let [key, value] of formData.entries()) {
            if (value !== '') {
                if (['internetLeads', 'appointmentsSet', 'showPercentage'].includes(key)) {
                    data[key] = value ? parseInt(value) : null;
                } else {
                    data[key] = value;
                }
            } else if (key === 'internetLeads' || key === 'showPercentage') {
                data[key] = null;
            }
        }
        
        try {
            const response = await fetch('/api/dealerships', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            if (response.ok) {
                showMessage('Dealership data saved successfully!', 'success');
                resetForm();
                loadRecentEntries();
            } else {
                const error = await response.json();
                showMessage(error.message || 'Error saving data', 'error');
            }
        } catch (error) {
            showMessage('Network error. Data saved locally.', 'warning');
            // Save to localStorage as fallback
            dataManager.addDealership(data);
            resetForm();
            loadRecentEntries();
        }
    });
    
    // Real-time conversion calculation
    function updateConversionPreview() {
        const leads = document.getElementById('internetLeads').value;
        const appointments = document.getElementById('appointmentsSet').value;
        
        if (leads && appointments) {
            const conversion = Math.round((appointments / leads) * 100);
            document.getElementById('previewConversion').textContent = conversion + '%';
            document.getElementById('conversionPreview').style.display = 'block';
        } else {
            document.getElementById('conversionPreview').style.display = 'none';
        }
    }
    
    // Add event listeners for real-time updates
    document.getElementById('internetLeads').addEventListener('input', updateConversionPreview);
    document.getElementById('appointmentsSet').addEventListener('input', updateConversionPreview);
    
    function resetForm() {
        document.getElementById('dealershipForm').reset();
        document.getElementById('conversionPreview').style.display = 'none';
    }
    
    function showMessage(message, type) {
        const container = document.getElementById('messageContainer');
        container.className = `message-container ${type}`;
        container.textContent = message;
        container.classList.remove('hidden');
        
        setTimeout(() => {
            container.classList.add('hidden');
        }, 5000);
    }
    
    function loadRecentEntries() {
        // This would load recent entries from your data source
        // For now, just a placeholder
        console.log('Loading recent entries...');
    }
    
    // Load recent entries on page load
    document.addEventListener('DOMContentLoaded', loadRecentEntries);
</script>
{% endblock %}
```

---

## 6. static/css/main.css
```css
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #2d3748;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.8rem;
    font-weight: 700;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.2);
    border-radius: 8px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.header-stats {
    display: flex;
    gap: 2rem;
    font-size: 0.9rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.4rem;
    font-weight: 600;
}

.stat-label {
    opacity: 0.8;
    font-size: 0.8rem;
}

/* Navigation */
.nav {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-secondary {
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.view-toggle {
    display: flex;
    background: #f7fafc;
    border-radius: 8px;
    padding: 4px;
}

.toggle-btn {
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.toggle-btn.active {
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    color: #667eea;
}

.search-filter {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-box {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    width: 250px;
    transition: border-color 0.3s ease;
}

.search-box:focus {
    outline: none;
    border-color: #667eea;
}

/* Main Content */
.main-content {
    padding: 2rem 0;
}

/* Table View */
.table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.table-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-title {
    font-size: 1.3rem;
    font-weight: 600;
}

.table-actions {
    display: flex;
    gap: 1rem;
}

.action-btn {
    padding: 0.5rem 1rem;
    background: rgba(255,255,255,0.2);
    border: none;
    border-radius: 6px;
    color: white;
    cursor: pointer;
    font-size: 0.85rem;
    transition: background 0.3s ease;
    text-decoration: none;
}

.action-btn:hover {
    background: rgba(255,255,255,0.3);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: #f8fafc;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #4a5568;
    border-bottom: 2px solid #e2e8f0;
    cursor: pointer;
    user-select: none;
}

.data-table th:hover {
    background: #edf2f7;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
    transition: background 0.3s ease;
}

.data-table tbody tr:hover {
    background: #f7fafc;
}

.dealership-name {
    font-weight: 600;
    color: #2d3748;
}

.brand-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 0.25rem;
}

.brand-chevrolet { background: #ffd700; color: #b7791f; }
.brand-kia { background: #dc143c; color: white; }
.brand-honda { background: #c41e3a; color: white; }
.brand-toyota { background: #eb0a1e; color: white; }
.brand-mitsubishi { background: #e60012; color: white; }
.brand-subaru { background: #0066cc; color: white; }
.brand-nissan { background: #000000; color: white; }
.brand-buick { background: #1f4788; color: white; }
.brand-gmc { background: #c8102e; color: white; }

.metric-value {
    font-weight: 600;
    font-size: 1.1rem;
}

.percentage {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    text-align: center;
}

.percentage.excellent { background: #c6f6d5; color: #22543d; }
.percentage.good { background: #bee3f8; color: #2c5282; }
.percentage.average { background: #feebc8; color: #c05621; }
.percentage.poor { background: #fed7d7; color: #c53030; }

.status-active {
    color: #38a169;
    font-weight: 600;
}

.status-inactive {
    color: #e53e3e;
    font-weight: 600;
}

/* Cards View */
.cards-grid {
    display: none;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.cards-grid.active {
    display: grid;
}

.dealership-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border-left: 4px solid #667eea;
}

.dealership-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.card-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.card-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.card-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.metric-box {
    text-align: center;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 8px;
}

.metric-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.25rem;
}

.metric-label {
    font-size: 0.8rem;
    color: #718096;
    font-weight: 500;
}

.card-performance {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 8px;
}

.performance-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
}

/* Summary Stats */
.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    text-align: center;
}

.summary-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.summary-label {
    color: #718096;
    font-weight: 500;
}

/* Data Entry Styles */
.data-entry-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.form-container {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
}

.form-header {
    margin-bottom: 2rem;
    text-align: center;
}

.form-header h2 {
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.form-header p {
    color: #718096;
}

.dealership-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-group small {
    color: #718096;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1rem;
}

.conversion-preview {
    background: #f7fafc;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.conversion-preview h3 {
    color: #4a5568;
    margin-bottom: 1rem;
}

.preview-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.recent-entries {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    height: fit-content;
}

.recent-entries h3 {
    color: #2d3748;
    margin-bottom: 1rem;
}

.recent-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.no-entries {
    color: #718096;
    text-align: center;
    padding: 2rem;
}

/* Messages */
.message-container {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    z-index: 1000;
    transition: all 0.3s ease;
}

.message-container.success {
    background: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.message-container.error {
    background: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
}

.message-container.warning {
    background: #feebc8;
    color: #c05621;
    border: 1px solid #fbd38d;
}

/* Loading States */
.loading {
    text-align: center;
    color: #718096;
    padding: 2rem;
}

.loading-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    color: #718096;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
    text-align: center;
    z-index: 1000;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hidden {
    display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .header-stats {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .nav-content {
        flex-direction: column;
        gap: 1rem;
    }

    .search-filter {
        flex-direction: column;
        width: 100%;
    }

    .search-box {
        width: 100%;
    }

    .data-table {
        font-size: 0.85rem;
    }

    .cards-grid {
        grid-template-columns: 1fr;
    }

    .data-entry-container {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}
```

---

## 7. static/js/dataManager.js
```javascript
class DataManager {
    constructor() {
        this.storageKey = 'truebdc_dealerships';
        this.statsKey = 'truebdc_stats';
        this.data = this.loadData();
    }

    loadData() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('Error loading data:', error);
            return [];
        }
    }

    saveData() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.data));
            this.updateStats();
        } catch (error) {
            console.error('Error saving data:', error);
        }
    }

    addDealership(dealership) {
        const newDealership = {
            ...dealership,
            id: Date.now(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        this.data.push(newDealership);
        this.saveData();
        return newDealership;
    }

    updateDealership(id, updates) {
        const index = this.data.findIndex(d => d.id === id);
        if (index !== -1) {
            this.data[index] = {
                ...this.data[index],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            this.saveData();
            return this.data[index];
        }
        return null;
    }

    deleteDealership(id) {
        const index = this.data.findIndex(d => d.id === id);
        if (index !== -1) {
            const deleted = this.data.splice(index, 1)[0];
            this.saveData();
            return deleted;
        }
        return null;
    }

    getAllDealerships() {
        return [...this.data];
    }

    getDealership(id) {
        return this.data.find(d => d.id === id);
    }

    calculateConversionRate(internetLeads, appointmentsSet) {
        if (!internetLeads || internetLeads === 0) return null;
        return Math.round((appointmentsSet / internetLeads) * 100);
    }

    getPerformanceClass(percentage) {
        if (!percentage) return '';
        if (percentage >= 60) return 'excellent';
        if (percentage >= 45) return 'good';
        if (percentage >= 30) return 'average';
        return 'poor';
    }

    getBrandClass(brand) {
        return `brand-${brand.toLowerCase().replace(/\s+/g, '-')}`;
    }

    calculateStats() {
        const activeDealerships = this.data.filter(d => d.status === 'active');
        const totalLeads = activeDealerships.reduce((sum, d) => sum + (d.internetLeads || 0), 0);
        const totalAppointments = activeDealerships.reduce((sum, d) => sum + (d.appointmentsSet || 0), 0);
        
        const dealershipsWithLeads = activeDealerships.filter(d => d.internetLeads && d.internetLeads > 0);
        const avgConversion = dealershipsWithLeads.length > 0 
            ? Math.round(dealershipsWithLeads.reduce((sum, d) => 
                sum + this.calculateConversionRate(d.internetLeads, d.appointmentsSet), 0) / dealershipsWithLeads.length)
            : 0;

        const dealershipsWithShow = activeDealerships.filter(d => d.showPercentage);
        const avgShow = dealershipsWithShow.length > 0
            ? Math.round(dealershipsWithShow.reduce((sum, d) => sum + d.showPercentage, 0) / dealershipsWithShow.length)
            : 0;

        return {
            totalDealerships: this.data.length,
            activeStores: activeDealerships.length,
            totalLeads,
            totalAppointments,
            averageConversion: avgConversion,
            averageShow: avgShow
        };
    }

    updateStats() {
        const stats = this.calculateStats();
        localStorage.setItem(this.statsKey, JSON.stringify(stats));
        
        // Update header stats if elements exist
        this.updateHeaderStats(stats);
    }

    updateHeaderStats(stats) {
        const elements = {
            totalLeads: document.getElementById('totalLeads'),
            totalAppointments: document.getElementById('totalAppointments'),
            averageConversion: document.getElementById('averageConversion'),
            activeStores: document.getElementById('activeStores')
        };

        if (elements.totalLeads) elements.totalLeads.textContent = stats.totalLeads.toLocaleString();
        if (elements.totalAppointments) elements.totalAppointments.textContent = stats.totalAppointments.toLocaleString();
        if (elements.averageConversion) elements.averageConversion.textContent = stats.averageConversion + '%';
        if (elements.activeStores) elements.activeStores.textContent = stats.activeStores;
    }

    getStats() {
        try {
            const stored = localStorage.getItem(this.statsKey);
            return stored ? JSON.parse(stored) : this.calculateStats();
        } catch (error) {
            console.error('Error loading stats:', error);
            return this.calculateStats();
        }
    }

    searchDealerships(query) {
        if (!query) return this.data;
        
        const searchTerm = query.toLowerCase();
        return this.data.filter(dealership => 
            dealership.name.toLowerCase().includes(searchTerm) ||
            dealership.brand.toLowerCase().includes(searchTerm)
        );
    }

    sortDealerships(field, direction = 'asc') {
        const sorted = [...this.data].sort((a, b) => {
            let aVal = a[field];
            let bVal = b[field];
            
            // Handle special cases
            if (field === 'conversionRate') {
                aVal = this.calculateConversionRate(a.internetLeads, a.appointmentsSet) || 0;
                bVal = this.calculateConversionRate(b.internetLeads, b.appointmentsSet) || 0;
            }
            
            // Handle null/undefined values
            if (aVal == null) aVal = direction === 'asc' ? -Infinity : Infinity;
            if (bVal == null) bVal = direction === 'asc' ? -Infinity : Infinity;
            
            if (direction === 'asc') {
                return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
            } else {
                return aVal < bVal ? 1 : aVal > bVal ? -1 : 0;
            }
        });
        
        return sorted;
    }

    exportToCSV() {
        const headers = [
            'Dealership Name',
            'Brand',
            'Internet Leads',
            'Appointments Set',
            'Conversion Rate %',
            'Show Rate %',
            'Status',
            'Created At',
            'Updated At'
        ];
        
        const rows = this.data.map(d => [
            d.name,
            d.brand,
            d.internetLeads || '',
            d.appointmentsSet || '',
            this.calculateConversionRate(d.internetLeads, d.appointmentsSet) || '',
            d.showPercentage || '',
            d.status,
            d.createdAt ? new Date(d.createdAt).toLocaleDateString() : '',
            d.updatedAt ? new Date(d.updatedAt).toLocaleDateString() : ''
        ]);
        
        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');
        
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `truebdc_data_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
    }
}

// Create global instance
const dataManager = new DataManager();
```

---

## 8. static/js/main.js
```javascript
// Global utility functions and initialization

// Initialize header stats on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    loadHeaderStats();
    
    // Initialize other components based on page
    if (window.location.pathname === '/' || window.location.pathname.includes('dashboard')) {
        initializeDashboard();
    }
}

async function loadHeaderStats() {
    try {
        // Try to fetch from API first
        const response = await fetch('/api/stats');
        if (response.ok) {
            const stats = await response.json();
            updateHeaderDisplay(stats);
        } else {
            // Fallback to localStorage
            const stats = dataManager.getStats();
            updateHeaderDisplay(stats);
        }
    } catch (error) {
        console.error('Error loading stats:', error);
        // Fallback to localStorage
        const stats = dataManager.getStats();
        updateHeaderDisplay(stats);
    }
}

function updateHeaderDisplay(stats) {
    const elements = {
        totalLeads: document.getElementById('totalLeads'),
        totalAppointments: document.getElementById('totalAppointments'),
        averageConversion: document.getElementById('averageConversion'),
        activeStores: document.getElementById('activeStores')
    };

    if (elements.totalLeads) {
        elements.totalLeads.textContent = stats.totalLeads ? stats.totalLeads.toLocaleString() : '—';
    }
    if (elements.totalAppointments) {
        elements.totalAppointments.textContent = stats.totalAppointments ? stats.totalAppointments.toLocaleString() : '—';
    }
    if (elements.averageConversion) {
        elements.averageConversion.textContent = stats.averageConversion ? stats.averageConversion + '%' : '—';
    }
    if (elements.activeStores) {
        elements.activeStores.textContent = stats.activeStores || '—';
    }
}

// Global utility functions
function showLoading(show = true) {
    const indicator = document.getElementById('loadingIndicator');
    if (indicator) {
        if (show) {
            indicator.classList.remove('hidden');
        } else {
            indicator.classList.add('hidden');
        }
    }
}

function showMessage(message, type = 'info', duration = 5000) {
    // Create message element if it doesn't exist
    let messageContainer = document.getElementById('messageContainer');
    if (!messageContainer) {
        messageContainer = document.createElement('div');
        messageContainer.id = 'messageContainer';
        messageContainer.className = 'message-container hidden';
        document.body.appendChild(messageContainer);
    }
    
    messageContainer.className = `message-container ${type}`;
    messageContainer.textContent = message;
    messageContainer.classList.remove('hidden');
    
    setTimeout(() => {
        messageContainer.classList.add('hidden');
    }, duration);
}

// Export functions
function exportData() {
    try {
        dataManager.exportToCSV();
        showMessage('Data exported successfully!', 'success');
    } catch (error) {
        console.error('Export error:', error);
        showMessage('Error exporting data', 'error');
    }
}

async function refreshData() {
    showLoading(true);
    try {
        await loadHeaderStats();
        if (typeof loadDashboardData === 'function') {
            await loadDashboardData();
        }
        showMessage('Data refreshed successfully!', 'success');
    } catch (error) {
        console.error('Refresh error:', error);
        showMessage('Error refreshing data', 'error');
    } finally {
        showLoading(false);
    }
}

// Navigation helpers
function navigateTo(url) {
    window.location.href = url;
}

// Format utilities
function formatNumber(number) {
    if (number == null) return '—';
    return number.toLocaleString();
}

function formatPercentage(value) {
    if (value == null) return '—';
    return value + '%';
}

function formatDate(dateString) {
    if (!dateString) return '—';
    return new Date(dateString).toLocaleDateString();
}
```

---

## 9. static/js/dashboard.js
```javascript
// Dashboard-specific functionality

let currentSortField = null;
let currentSortDirection = 'asc';
let currentView = 'table';
let dealershipData = [];

function initializeDashboard() {
    setupViewToggle();
    setupSearch();
    loadDashboardData();
    loadSummaryStats();
}

function setupViewToggle() {
    const toggleBtns = document.querySelectorAll('.toggle-btn');
    const tableView = document.getElementById('tableView');
    const cardsView = document.getElementById('cardsView');

    toggleBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            toggleBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');

            currentView = btn.dataset.view;
            if (currentView === 'table') {
                tableView.style.display = 'block';
                cardsView.classList.remove('active');
            } else {
                tableView.style.display = 'none';
                cardsView.classList.add('active');
            }
        });
    });
}

function setupSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce((e) => {
            const searchTerm = e.target.value;
            filterAndRenderData(searchTerm);
        }, 300));
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

async function loadDashboardData() {
    showLoading(true);
    
    try {
        // Try to fetch from API first
        const response = await fetch('/api/dealerships');
        if (response.ok) {
            dealershipData = await response.json();
        } else {
            // Fallback to localStorage
            dealershipData = dataManager.getAllDealerships();
        }
        
        renderTable(dealershipData);
        renderCards(dealershipData);
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        // Fallback to localStorage
        dealershipData = dataManager.getAllDealerships();
        renderTable(dealershipData);
        renderCards(dealershipData);
        showMessage('Using offline data', 'warning');
    } finally {
        showLoading(false);
    }
}

async function loadSummaryStats() {
    try {
        const response = await fetch('/api/stats');
        if (response.ok) {
            const stats = await response.json();
            updateSummaryDisplay(stats);
        } else {
            const stats = dataManager.getStats();
            updateSummaryDisplay(stats);
        }
    } catch (error) {
        console.error('Error loading summary stats:', error);
        const stats = dataManager.getStats();
        updateSummaryDisplay(stats);
    }
}

function updateSummaryDisplay(stats) {
    const elements = {
        summaryTotalDealerships: document.getElementById('summaryTotalDealerships'),
        summaryActiveStores: document.getElementById('summaryActiveStores'),
        summaryAvgConversion: document.getElementById('summaryAvgConversion'),
        summaryAvgShow: document.getElementById('summaryAvgShow')
    };

    if (elements.summaryTotalDealerships) {
        elements.summaryTotalDealerships.textContent = stats.totalDealerships || '—';
    }
    if (elements.summaryActiveStores) {
        elements.summaryActiveStores.textContent = stats.activeStores || '—';
    }
    if (elements.summaryAvgConversion) {
        elements.summaryAvgConversion.textContent = stats.averageConversion ? stats.averageConversion + '%' : '—';
    }
    if (elements.summaryAvgShow) {
        elements.summaryAvgShow.textContent = stats.averageShow ? stats.averageShow + '%' : '—';
    }
}

function renderTable(data) {
    const tbody = document.getElementById('tableBody');
    if (!tbody) return;
    
    tbody.innerHTML = '';

    if (data.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="loading">No dealerships found</td></tr>';
        return;
    }

    data.forEach(dealership => {
        const conversionRate = dataManager.calculateConversionRate(
            dealership.internetLeads, 
            dealership.appointmentsSet
        );
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <div class="dealership-name">${dealership.name}</div>
                <div class="brand-badge ${dataManager.getBrandClass(dealership.brand)}">${dealership.brand}</div>
            </td>
            <td>
                <span class="metric-value">${dealership.internetLeads || '—'}</span>
            </td>
            <td>
                <span class="metric-value">${dealership.appointmentsSet || '—'}</span>
            </td>
            <td>
                ${conversionRate ? 
                    `<span class="percentage ${dataManager.getPerformanceClass(conversionRate)}">${conversionRate}%</span>` : 
                    '<span class="percentage">—</span>'
                }
            </td>
            <td>
                ${dealership.showPercentage ? 
                    `<span class="percentage ${dataManager.getPerformanceClass(dealership.showPercentage)}">${dealership.showPercentage}%</span>` : 
                    '<span class="percentage">—</span>'
                }
            </td>
            <td>
                <span class="status-${dealership.status}">${dealership.status.charAt(0).toUpperCase() + dealership.status.slice(1)}</span>
            </td>
            <td>
                <button class="action-btn" onclick="editDealership(${dealership.id})">Edit</button>
                <button class="action-btn" onclick="deleteDealership(${dealership.id})" style="background: rgba(220, 53, 69, 0.2);">Delete</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function renderCards(data) {
    const cardsContainer = document.getElementById('cardsView');
    if (!cardsContainer) return;
    
    cardsContainer.innerHTML = '';

    if (data.length === 0) {
        cardsContainer.innerHTML = '<div class="loading-card">No dealerships found</div>';
        return;
    }

    data.forEach(dealership => {
        const conversionRate = dataManager.calculateConversionRate(
            dealership.internetLeads, 
            dealership.appointmentsSet
        );
        
        const card = document.createElement('div');
        card.className = 'dealership-card';
        card.onclick = () => viewDealershipDetails(dealership.id);
        
        card.innerHTML = `
            <div class="card-header">
                <div>
                    <div class="card-title">${dealership.name}</div>
                    <div class="brand-badge ${dataManager.getBrandClass(dealership.brand)}">${dealership.brand}</div>
                </div>
                <div class="card-status status-${dealership.status}">
                    ${dealership.status === 'active' ? '🟢' : '🔴'} ${dealership.status.charAt(0).toUpperCase() + dealership.status.slice(1)}
                </div>
            </div>
            
            <div class="card-metrics">
                <div class="metric-box">
                    <div class="metric-number">${dealership.internetLeads || '—'}</div>
                    <div class="metric-label">Internet Leads</div>
                </div>
                <div class="metric-box">
                    <div class="metric-number">${dealership.appointmentsSet || '—'}</div>
                    <div class="metric-label">Appointments</div>
                </div>
            </div>
            
            <div class="card-performance">
                <div class="performance-indicator">
                    <span>Lead Conversion:</span>
                    <span class="percentage ${dataManager.getPerformanceClass(conversionRate)}">
                        ${conversionRate ? conversionRate + '%' : '—'}
                    </span>
                </div>
                <div class="performance-indicator">
                    <span>Show Rate:</span>
                    <span class="percentage ${dataManager.getPerformanceClass(dealership.showPercentage)}">
                        ${dealership.showPercentage ? dealership.showPercentage + '%' : '—'}
                    </span>
                </div>
            </div>
        `;
        
        cardsContainer.appendChild(card);
    });
}

function sortTable(columnIndex) {
    const columns = ['name', 'internetLeads', 'appointmentsSet', 'conversionRate', 'showPercentage', 'status'];
    const field = columns[columnIndex];
    
    if (currentSortField === field) {
        currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        currentSortField = field;
        currentSortDirection = 'asc';
    }
    
    const sortedData = dataManager.sortDealerships(field, currentSortDirection);
    renderTable(sortedData);
    renderCards(sortedData);
}

function filterAndRenderData(searchTerm) {
    let filteredData = dealershipData;
    
    if (searchTerm) {
        filteredData = dataManager.searchDealerships(searchTerm);
    }
    
    renderTable(filteredData);
    renderCards(filteredData);
}

function editDealership(id) {
    // Navigate to edit form or open modal
    console.log('Edit dealership:', id);
    // For now, just navigate to data entry page
    // You could also implement a modal edit form
    navigateTo(`/data-entry?edit=${id}`);
}

async function deleteDealership(id) {
    if (!confirm('Are you sure you want to delete this dealership?')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/dealerships/${id}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            dataManager.deleteDealership(id);
            await loadDashboardData();
            await loadSummaryStats();
            await loadHeaderStats();
            showMessage('Dealership deleted successfully!', 'success');
        } else {
            throw new Error('Failed to delete from server');
        }
    } catch (error) {
        console.error('Delete error:', error);
        // Fallback to local deletion
        dataManager.deleteDealership(id);
        await loadDashboardData();
        await loadSummaryStats();
        await loadHeaderStats();
        showMessage('Dealership deleted locally', 'warning');
    }
}

function viewDealershipDetails(id) {
    // Navigate to detailed view
    console.log('View dealership details:', id);
    // This would navigate to a detailed dealership page
    // navigateTo(`/dealership/${id}`);
}

// Make functions available globally
window.sortTable = sortTable;
window.editDealership = editDealership;
window.deleteDealership = deleteDealership;
window.viewDealershipDetails = viewDealershipDetails;
window.loadDashboardData = loadDashboardData;
```

---

## Setup Instructions

1. **Create the directory structure**:
```bash
mkdir truebdc-dashboard
cd truebdc-dashboard
mkdir templates static static/css static/js
```

2. **Save each file** in its respective location

3. **Install Flask**:
```bash
pip install -r requirements.txt
```

4. **Run the application**:
```bash
python app.py
```

5. **Open your browser** and go to `http://localhost:5000`

The application will start with the dashboard view and you can navigate to the data entry form to add dealership data manually. All data will be stored in localStorage for now, and you can easily migrate to Airtable later by updating the API endpoints in `app.py`.
