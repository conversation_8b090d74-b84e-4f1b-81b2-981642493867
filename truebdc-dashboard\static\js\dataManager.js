class DataManager {
    constructor() {
        this.storageKey = 'truebdc_dealerships';
        this.statsKey = 'truebdc_stats';
        this.data = this.loadData();
    }

    loadData() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('Error loading data:', error);
            return [];
        }
    }

    saveData() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.data));
            this.updateStats();
        } catch (error) {
            console.error('Error saving data:', error);
        }
    }

    addDealership(dealership) {
        const newDealership = {
            ...dealership,
            id: Date.now(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        this.data.push(newDealership);
        this.saveData();
        return newDealership;
    }

    updateDealership(id, updates) {
        const index = this.data.findIndex(d => d.id === id);
        if (index !== -1) {
            this.data[index] = {
                ...this.data[index],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            this.saveData();
            return this.data[index];
        }
        return null;
    }

    deleteDealership(id) {
        const index = this.data.findIndex(d => d.id === id);
        if (index !== -1) {
            const deleted = this.data.splice(index, 1)[0];
            this.saveData();
            return deleted;
        }
        return null;
    }

    getAllDealerships() {
        return [...this.data];
    }

    getDealership(id) {
        return this.data.find(d => d.id === id);
    }

    calculateConversionRate(internetLeads, appointmentsSet) {
        if (!internetLeads || internetLeads === 0) return null;
        return Math.round((appointmentsSet / internetLeads) * 100);
    }

    getPerformanceClass(percentage) {
        if (!percentage) return '';
        if (percentage >= 60) return 'excellent';
        if (percentage >= 45) return 'good';
        if (percentage >= 30) return 'average';
        return 'poor';
    }

    getBrandClass(brand) {
        return `brand-${brand.toLowerCase().replace(/\s+/g, '-')}`;
    }

    calculateStats() {
        const activeDealerships = this.data.filter(d => d.status === 'active');
        const totalLeads = activeDealerships.reduce((sum, d) => sum + (d.internetLeads || 0), 0);
        const totalAppointments = activeDealerships.reduce((sum, d) => sum + (d.appointmentsSet || 0), 0);
        
        const dealershipsWithLeads = activeDealerships.filter(d => d.internetLeads && d.internetLeads > 0);
        const avgConversion = dealershipsWithLeads.length > 0 
            ? Math.round(dealershipsWithLeads.reduce((sum, d) => 
                sum + this.calculateConversionRate(d.internetLeads, d.appointmentsSet), 0) / dealershipsWithLeads.length)
            : 0;

        const dealershipsWithShow = activeDealerships.filter(d => d.showPercentage);
        const avgShow = dealershipsWithShow.length > 0
            ? Math.round(dealershipsWithShow.reduce((sum, d) => sum + d.showPercentage, 0) / dealershipsWithShow.length)
            : 0;

        return {
            totalDealerships: this.data.length,
            activeStores: activeDealerships.length,
            totalLeads,
            totalAppointments,
            averageConversion: avgConversion,
            averageShow: avgShow
        };
    }

    updateStats() {
        const stats = this.calculateStats();
        localStorage.setItem(this.statsKey, JSON.stringify(stats));
        
        // Update header stats if elements exist
        this.updateHeaderStats(stats);
    }

    updateHeaderStats(stats) {
        const elements = {
            totalLeads: document.getElementById('totalLeads'),
            totalAppointments: document.getElementById('totalAppointments'),
            averageConversion: document.getElementById('averageConversion'),
            activeStores: document.getElementById('activeStores')
        };

        if (elements.totalLeads) elements.totalLeads.textContent = stats.totalLeads.toLocaleString();
        if (elements.totalAppointments) elements.totalAppointments.textContent = stats.totalAppointments.toLocaleString();
        if (elements.averageConversion) elements.averageConversion.textContent = stats.averageConversion + '%';
        if (elements.activeStores) elements.activeStores.textContent = stats.activeStores;
    }

    getStats() {
        try {
            const stored = localStorage.getItem(this.statsKey);
            return stored ? JSON.parse(stored) : this.calculateStats();
        } catch (error) {
            console.error('Error loading stats:', error);
            return this.calculateStats();
        }
    }

    searchDealerships(query) {
        if (!query) return this.data;
        
        const searchTerm = query.toLowerCase();
        return this.data.filter(dealership => 
            dealership.name.toLowerCase().includes(searchTerm) ||
            dealership.brand.toLowerCase().includes(searchTerm)
        );
    }

    sortDealerships(field, direction = 'asc') {
        const sorted = [...this.data].sort((a, b) => {
            let aVal = a[field];
            let bVal = b[field];
            
            // Handle special cases
            if (field === 'conversionRate') {
                aVal = this.calculateConversionRate(a.internetLeads, a.appointmentsSet) || 0;
                bVal = this.calculateConversionRate(b.internetLeads, b.appointmentsSet) || 0;
            }
            
            // Handle null/undefined values
            if (aVal == null) aVal = direction === 'asc' ? -Infinity : Infinity;
            if (bVal == null) bVal = direction === 'asc' ? -Infinity : Infinity;
            
            if (direction === 'asc') {
                return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
            } else {
                return aVal < bVal ? 1 : aVal > bVal ? -1 : 0;
            }
        });
        
        return sorted;
    }

    exportToCSV() {
        const headers = [
            'Dealership Name',
            'Brand',
            'Internet Leads',
            'Appointments Set',
            'Conversion Rate %',
            'Show Rate %',
            'Status',
            'Created At',
            'Updated At'
        ];
        
        const rows = this.data.map(d => [
            d.name,
            d.brand,
            d.internetLeads || '',
            d.appointmentsSet || '',
            this.calculateConversionRate(d.internetLeads, d.appointmentsSet) || '',
            d.showPercentage || '',
            d.status,
            d.createdAt ? new Date(d.createdAt).toLocaleDateString() : '',
            d.updatedAt ? new Date(d.updatedAt).toLocaleDateString() : ''
        ]);
        
        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');
        
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `truebdc_data_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
    }
}

// Create global instance
const dataManager = new DataManager();
