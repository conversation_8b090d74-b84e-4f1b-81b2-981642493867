{% extends "base.html" %}

{% block title %}Dashboard - TrueBDC{% endblock %}

{% block navigation_extra %}
<div class="search-filter">
    <input type="text" class="search-box" placeholder="Search dealerships..." id="searchInput">
    <div class="view-toggle">
        <button class="toggle-btn active" data-view="table">📋 Table</button>
        <button class="toggle-btn" data-view="cards">🏢 Cards</button>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Current Period Display -->
<div class="period-display-container">
    <div class="period-display">
        <span class="period-label">📅 Current Period:</span>
        <span id="currentPeriod" class="period-text">Loading...</span>
        <span class="period-note">Monthly goal tracking</span>
    </div>
</div>

<!-- Summary Statistics -->
<div class="summary-stats">
    <div class="summary-card">
        <div class="summary-number" id="summaryTotalLeads">—</div>
        <div class="summary-label">Total Leads</div>
    </div>
    <div class="summary-card">
        <div class="summary-number" id="summaryTotalAppointments">—</div>
        <div class="summary-label">Appointments</div>
    </div>
    <div class="summary-card">
        <div class="summary-number" id="summaryAvgConversion">—</div>
        <div class="summary-label">Avg Conversion</div>
    </div>
    <div class="summary-card">
        <div class="summary-number" id="summaryActiveStores">—</div>
        <div class="summary-label">Active Stores</div>
    </div>
</div>



<!-- Table View -->
<div class="table-container" id="tableView">
    <div class="table-header">
        <div class="table-title">📊 Dealership Performance - Month to Date</div>
        <div class="table-actions">
            <button class="action-btn" onclick="exportData()">📤 Export</button>
            <button class="action-btn" onclick="refreshData()">🔄 Refresh</button>
            <a href="{{ url_for('data_entry') }}" class="action-btn">📝 Add Entry</a>
        </div>
    </div>
    <table class="data-table">
        <thead>
            <tr>
                <th onclick="sortTable(0)">Dealership ↕</th>
                <th onclick="sortTable(1)">Total Internet Leads ↕</th>
                <th onclick="sortTable(2)">Total Appointments Set ↕</th>
                <th onclick="sortTable(3)">Daily Goal ↕</th>
                <th onclick="sortTable(4)">Tomorrow Appointments ↕</th>
                <th onclick="sortTable(5)">Monthly Goal ↕</th>
                <th onclick="sortTable(6)">Goal Progress ↕</th>
                <th onclick="sortTable(7)">Appointment/Lead % ↕</th>
                <th onclick="sortTable(8)">Show % According to CRM ↕</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody id="tableBody">
            <tr>
                <td colspan="10" class="loading">Loading data...</td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Cards View -->
<div class="cards-grid" id="cardsView">
    <div class="loading-card">Loading dealerships...</div>
</div>



<!-- Loading indicator -->
<div id="loadingIndicator" class="loading-indicator hidden">
    <div class="spinner"></div>
    <p>Loading data...</p>
</div>

<!-- Edit Dealership Modal -->
<div id="editModal" class="modal hidden">
    <div class="modal-content">
        <div class="modal-header">
            <h3>✏️ Edit Dealership</h3>
            <button class="modal-close" onclick="closeEditModal()">&times;</button>
        </div>
        <form id="editForm" class="edit-form">
            <input type="hidden" id="editId" name="id">

            <div class="form-row">
                <div class="form-group">
                    <label for="editName">Dealership Name *</label>
                    <input type="text" id="editName" name="name" required>
                </div>
                <div class="form-group">
                    <label for="editCrm">CRM System</label>
                    <select id="editCrm" name="crm">
                        <option value="">Select CRM</option>
                        <option value="eLeads">eLeads</option>
                        <option value="VinSolution">VinSolution</option>
                        <option value="DriveCentric">DriveCentric</option>
                        <option value="DealerSocket">DealerSocket</option>
                        <option value="Reynolds & Reynolds">Reynolds & Reynolds</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="editInternetLeads">Internet Leads</label>
                    <input type="number" id="editInternetLeads" name="internetLeads" min="0">
                </div>
                <div class="form-group">
                    <label for="editAppointmentsSet">Appointments Set *</label>
                    <input type="number" id="editAppointmentsSet" name="appointmentsSet" min="0" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="editShowPercentage">Show %</label>
                    <input type="number" id="editShowPercentage" name="showPercentage" min="0" max="100" step="0.1">
                </div>
                <div class="form-group">
                    <label for="editMonthlyGoal">Monthly Goal</label>
                    <input type="number" id="editMonthlyGoal" name="monthlyGoal" min="0">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="editDailyGoal">Daily Goal</label>
                    <input type="number" id="editDailyGoal" name="dailyGoal" min="0" max="100">
                </div>
                <div class="form-group">
                    <label for="editTomorrowAppointments">Tomorrow Appointments</label>
                    <input type="number" id="editTomorrowAppointments" name="tomorrowAppointments" min="0" max="100">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="editOperatingDays">Operating Days</label>
                    <select id="editOperatingDays" name="operatingDays">
                        <option value="mon-sat">Monday - Saturday</option>
                        <option value="mon-sun">Monday - Sunday</option>
                    </select>
                </div>
                <div class="form-group">
                    <!-- Empty space for layout balance -->
                </div>
            </div>

            <div class="form-row">
                <div class="form-group full-width">
                    <label for="editNotes">Notes (Optional)</label>
                    <textarea id="editNotes" name="notes" rows="3" placeholder="Additional notes or comments"></textarea>
                </div>
            </div>

            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" onclick="closeEditModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">💾 Save Changes</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/dateUtils.js') }}"></script>

<script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
{% endblock %}
