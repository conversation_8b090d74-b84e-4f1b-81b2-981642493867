// Global utility functions and initialization

// Initialize header stats on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    loadHeaderStats();
    initializeAnimations();

    // Initialize other components based on page
    if (window.location.pathname === '/' || window.location.pathname.includes('dashboard')) {
        initializeDashboard();
    }
}

function initializeAnimations() {
    // Add staggered animations to elements
    const animatedElements = document.querySelectorAll('.summary-card, .dealership-card, .table-container');
    animatedElements.forEach((element, index) => {
        element.classList.add('animate-fade-in-up');
        element.style.animationDelay = `${index * 0.1}s`;
    });

    // Initialize intersection observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in-up');
            }
        });
    }, observerOptions);

    // Observe elements that should animate on scroll
    const scrollElements = document.querySelectorAll('.form-container, .recent-entries, .data-entry-container > *');
    scrollElements.forEach(el => observer.observe(el));
}

async function loadHeaderStats() {
    try {
        // Try to fetch from API first
        const response = await fetch('/api/stats');
        if (response.ok) {
            const stats = await response.json();
            updateHeaderDisplay(stats);
        } else {
            // Fallback to localStorage
            const stats = dataManager.getStats();
            updateHeaderDisplay(stats);
        }
    } catch (error) {
        console.error('Error loading stats:', error);
        // Fallback to localStorage
        const stats = dataManager.getStats();
        updateHeaderDisplay(stats);
    }
}

function updateHeaderDisplay(stats) {
    const elements = {
        totalLeads: document.getElementById('totalLeads'),
        totalAppointments: document.getElementById('totalAppointments'),
        averageConversion: document.getElementById('averageConversion'),
        activeStores: document.getElementById('activeStores')
    };

    // Animate numbers with counting effect
    if (elements.totalLeads && stats.totalLeads) {
        animateNumber(elements.totalLeads, 0, stats.totalLeads, 1500, true);
    }
    if (elements.totalAppointments && stats.totalAppointments) {
        animateNumber(elements.totalAppointments, 0, stats.totalAppointments, 1500, true);
    }
    if (elements.averageConversion && stats.averageConversion) {
        animateNumber(elements.averageConversion, 0, stats.averageConversion, 1200, false, '%');
    }
    if (elements.activeStores && stats.activeStores) {
        animateNumber(elements.activeStores, 0, stats.activeStores, 1000);
    }
}

function animateNumber(element, start, end, duration, useCommas = false, suffix = '') {
    const startTime = performance.now();
    const range = end - start;

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Use easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.floor(start + (range * easeOutQuart));

        let displayValue = useCommas ? current.toLocaleString() : current.toString();
        if (suffix) displayValue += suffix;

        element.textContent = displayValue;
        element.classList.add('animate-count-up');

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        } else {
            // Ensure final value is exact
            let finalValue = useCommas ? end.toLocaleString() : end.toString();
            if (suffix) finalValue += suffix;
            element.textContent = finalValue;
        }
    }

    requestAnimationFrame(updateNumber);
}

// Global utility functions
function showLoading(show = true) {
    const indicator = document.getElementById('loadingIndicator');
    if (indicator) {
        if (show) {
            indicator.classList.remove('hidden');
        } else {
            indicator.classList.add('hidden');
        }
    }
}

function showMessage(message, type = 'info', duration = 5000) {
    // Create message element if it doesn't exist
    let messageContainer = document.getElementById('messageContainer');
    if (!messageContainer) {
        messageContainer = document.createElement('div');
        messageContainer.id = 'messageContainer';
        messageContainer.className = 'message-container hidden';
        document.body.appendChild(messageContainer);
    }
    
    messageContainer.className = `message-container ${type}`;
    messageContainer.textContent = message;
    messageContainer.classList.remove('hidden');
    
    setTimeout(() => {
        messageContainer.classList.add('hidden');
    }, duration);
}

// Export functions
function exportData() {
    try {
        dataManager.exportToCSV();
        showMessage('Data exported successfully!', 'success');
    } catch (error) {
        console.error('Export error:', error);
        showMessage('Error exporting data', 'error');
    }
}

async function refreshData() {
    showLoading(true);
    try {
        await loadHeaderStats();
        if (typeof loadDashboardData === 'function') {
            await loadDashboardData();
        }
        showMessage('Data refreshed successfully!', 'success');
    } catch (error) {
        console.error('Refresh error:', error);
        showMessage('Error refreshing data', 'error');
    } finally {
        showLoading(false);
    }
}

// Navigation helpers
function navigateTo(url) {
    window.location.href = url;
}

// Format utilities
function formatNumber(number) {
    if (number == null) return '—';
    return number.toLocaleString();
}

function formatPercentage(value) {
    if (value == null) return '—';
    return value + '%';
}

function formatDate(dateString) {
    if (!dateString) return '—';
    return new Date(dateString).toLocaleDateString();
}
