/**
 * Date Utility Module for TrueBDC Dashboard
 * Handles dealership-specific business day logic and date formatting
 */

class DateUtils {
    // Operating day constants
    static OPERATING_DAYS = {
        MON_SAT: 'mon-sat',  // Monday through Saturday
        MON_SUN: 'mon-sun'   // Monday through Sunday (7 days)
    };

    /**
     * Get current date information
     */
    static getCurrentDate() {
        return new Date();
    }

    /**
     * Get tomorrow's date
     */
    static getTomorrowDate() {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        return tomorrow;
    }

    /**
     * Get enhanced current period information
     * Returns format: "August 2025 • Day 4 of 31"
     */
    static getCurrentPeriodInfo() {
        const now = new Date();
        const monthNames = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];
        
        const month = now.getMonth();
        const year = now.getFullYear();
        const dayOfMonth = now.getDate();
        
        // Get total days in current month
        const daysInMonth = new Date(year, month + 1, 0).getDate();
        
        const monthName = monthNames[month];
        
        return {
            month: month + 1,
            year: year,
            dayOfMonth: dayOfMonth,
            daysInMonth: daysInMonth,
            monthName: monthName,
            formatted: `${monthName} ${year} • Day ${dayOfMonth} of ${daysInMonth}`,
            simple: `${monthName} ${year}`
        };
    }

    /**
     * Get day of week (0 = Sunday, 1 = Monday, etc.)
     */
    static getDayOfWeek(date = new Date()) {
        return date.getDay();
    }

    /**
     * Check if a date is a business day for a dealership
     * @param {Date} date - The date to check
     * @param {string} operatingDays - 'mon-sat' or 'mon-sun'
     */
    static isBusinessDay(date, operatingDays = this.OPERATING_DAYS.MON_SAT) {
        const dayOfWeek = this.getDayOfWeek(date);

        if (operatingDays === this.OPERATING_DAYS.MON_SUN) {
            // Monday through Sunday (always open)
            return true;
        } else {
            // Monday through Saturday (closed Sunday)
            return dayOfWeek >= 1 && dayOfWeek <= 6;
        }
    }

    /**
     * Get the next business day for a dealership
     * @param {string} operatingDays - 'mon-sat' or 'mon-sun'
     */
    static getNextBusinessDay(operatingDays = this.OPERATING_DAYS.MON_SAT) {
        const tomorrow = this.getTomorrowDate();
        
        if (operatingDays === this.OPERATING_DAYS.MON_SUN) {
            // Always open, so tomorrow is always next business day
            return tomorrow;
        }
        
        // For mon-sat dealerships, skip Sunday
        const dayOfWeek = this.getDayOfWeek(tomorrow);
        if (dayOfWeek === 0) { // Sunday
            const monday = new Date(tomorrow);
            monday.setDate(monday.getDate() + 1);
            return monday;
        }
        
        return tomorrow;
    }

    /**
     * Get tomorrow's appointment information with smart business logic
     * @param {string} operatingDays - 'mon-sat' or 'mon-sun'
     */
    static getTomorrowInfo(operatingDays = this.OPERATING_DAYS.MON_SAT) {
        const today = new Date();
        const tomorrow = this.getTomorrowDate();
        const nextBusinessDay = this.getNextBusinessDay(operatingDays);
        
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const shortDayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        
        const isTomorrowBusinessDay = this.isBusinessDay(tomorrow, operatingDays);
        const targetDate = isTomorrowBusinessDay ? tomorrow : nextBusinessDay;
        
        const dayName = dayNames[targetDate.getDay()];
        const shortDayName = shortDayNames[targetDate.getDay()];
        const monthName = monthNames[targetDate.getMonth()];
        const dayOfMonth = targetDate.getDate();
        
        // Determine relative text
        let relativeText = 'Tomorrow';
        if (!isTomorrowBusinessDay) {
            relativeText = 'Next Business Day';
        }
        
        return {
            date: targetDate,
            dayName: dayName,
            shortDayName: shortDayName,
            monthName: monthName,
            dayOfMonth: dayOfMonth,
            relativeText: relativeText,
            isTomorrow: isTomorrowBusinessDay,
            isBusinessDay: true,
            // Formatted strings for different contexts
            compact: `${shortDayName}`, // "Mon"
            compactWithDate: `${monthName} ${dayOfMonth}`, // "Aug 5"
            tableFormat: `(${monthName} ${dayOfMonth})`, // "(Aug 5)"
            cardFormat: `${relativeText}: {count} appointments (${shortDayName}, ${monthName} ${dayOfMonth})`, // "Tomorrow: 8 appointments (Mon, Aug 5)"
            fullFormat: `${dayName}, ${monthName} ${dayOfMonth}` // "Monday, Aug 5"
        };
    }

    /**
     * Format a date for display
     * @param {Date} date 
     * @param {string} format - 'short', 'medium', 'long'
     */
    static formatDate(date, format = 'medium') {
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const shortDayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const fullMonthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        
        const dayName = dayNames[date.getDay()];
        const shortDayName = shortDayNames[date.getDay()];
        const monthName = monthNames[date.getMonth()];
        const fullMonthName = fullMonthNames[date.getMonth()];
        const dayOfMonth = date.getDate();
        const year = date.getFullYear();
        
        switch (format) {
            case 'short':
                return `${monthName} ${dayOfMonth}`;
            case 'medium':
                return `${shortDayName}, ${monthName} ${dayOfMonth}`;
            case 'long':
                return `${dayName}, ${fullMonthName} ${dayOfMonth}, ${year}`;
            default:
                return `${shortDayName}, ${monthName} ${dayOfMonth}`;
        }
    }

    /**
     * Parse various date input formats
     * @param {string} input - Date string in various formats
     */
    static parseDate(input) {
        if (!input) return null;
        
        const today = new Date();
        const inputLower = input.toLowerCase().trim();
        
        // Handle relative dates
        if (inputLower === 'today') {
            return today;
        }
        if (inputLower === 'tomorrow') {
            return this.getTomorrowDate();
        }
        
        // Handle day names (next Monday, etc.)
        const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        for (let i = 0; i < dayNames.length; i++) {
            if (inputLower.includes(dayNames[i])) {
                const targetDay = i;
                const currentDay = today.getDay();
                let daysToAdd = targetDay - currentDay;
                if (daysToAdd <= 0) daysToAdd += 7; // Next occurrence
                
                const targetDate = new Date(today);
                targetDate.setDate(today.getDate() + daysToAdd);
                return targetDate;
            }
        }
        
        // Try standard date parsing
        const parsed = new Date(input);
        if (!isNaN(parsed.getTime())) {
            return parsed;
        }
        
        return null;
    }

    /**
     * Get operating days display text
     * @param {string} operatingDays 
     */
    static getOperatingDaysText(operatingDays) {
        switch (operatingDays) {
            case this.OPERATING_DAYS.MON_SUN:
                return 'Monday - Sunday';
            case this.OPERATING_DAYS.MON_SAT:
            default:
                return 'Monday - Saturday';
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DateUtils;
}
