// Enhanced Form UX functionality

class FormEnhancer {
    constructor() {
        this.validationRules = {
            required: (value) => value.trim() !== '',
            email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
            phone: (value) => /^[\+]?[1-9][\d]{0,15}$/.test(value.replace(/[\s\-\(\)]/g, '')),
            number: (value) => !isNaN(value) && value !== '',
            min: (value, min) => parseFloat(value) >= min,
            max: (value, max) => parseFloat(value) <= max,
            minLength: (value, length) => value.length >= length,
            maxLength: (value, length) => value.length <= length
        };

        this.messages = {
            required: 'This field is required',
            email: 'Please enter a valid email address',
            phone: 'Please enter a valid phone number',
            number: 'Please enter a valid number',
            min: 'Value must be at least {min}',
            max: 'Value must be no more than {max}',
            minLength: 'Must be at least {length} characters',
            maxLength: 'Must be no more than {length} characters'
        };

        this.autocompleteData = {
            dealershipName: [
                'All American Chevrolet',
                'Archer Kia',
                'AutoNation Chevrolet Delray',
                'AutoNation Honda Delray',
                'AutoNation Subaru Delray',
                'Boca Honda',
                'Boca Kia',
                'Boca Subaru',
                'Boynton Beach Honda',
                'Braman Honda',
                'Braman Kia',
                'Coconut Creek Subaru',
                'Coral Springs Honda',
                'Coral Springs Kia',
                'Delray Kia',
                'Doral Honda',
                'Doral Kia',
                'Fort Lauderdale Honda',
                'Hialeah Honda',
                'Kendall Honda'
            ],
            brand: ['Chevrolet', 'Honda', 'Kia', 'Subaru'],
            leadSource: ['Website', 'Social Media', 'Referrals', 'Direct', 'Phone', 'Email', 'Walk-in']
        };
    }

    init() {
        this.enhanceAllForms();
        this.setupGlobalValidation();
    }

    enhanceAllForms() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => this.enhanceForm(form));
    }

    enhanceForm(form) {
        // Add progress indicator
        this.addProgressIndicator(form);
        
        // Enhance form groups
        const formGroups = form.querySelectorAll('.form-group');
        formGroups.forEach(group => this.enhanceFormGroup(group));
        
        // Setup form submission
        this.setupFormSubmission(form);
    }

    enhanceFormGroup(group) {
        const input = group.querySelector('input, select, textarea');
        const label = group.querySelector('label');
        
        if (!input) return;

        // Add floating label functionality
        if (label && input.type !== 'checkbox' && input.type !== 'radio') {
            this.setupFloatingLabel(group, input, label);
        }

        // Add validation feedback elements
        this.addValidationElements(group);
        
        // Setup real-time validation
        this.setupRealTimeValidation(group, input);
        
        // Setup autocomplete if applicable
        this.setupAutocomplete(group, input);
        
        // Add enhanced focus effects
        this.setupFocusEffects(input);
    }

    setupFloatingLabel(group, input, label) {
        group.classList.add('floating-label');
        
        // Move label after input for CSS positioning
        input.parentNode.insertBefore(label, input.nextSibling);
        
        // Add placeholder for floating label effect
        if (!input.placeholder) {
            input.placeholder = ' ';
        }
    }

    addValidationElements(group) {
        // Add feedback icon
        const feedback = document.createElement('div');
        feedback.className = 'form-feedback';
        group.appendChild(feedback);
        
        // Add message container
        const message = document.createElement('div');
        message.className = 'form-message';
        group.appendChild(message);
    }

    setupRealTimeValidation(group, input) {
        const validateField = () => {
            this.validateField(group, input);
            this.updateFormProgress(input.form);
        };

        // Validate on blur for better UX
        input.addEventListener('blur', validateField);
        
        // Validate on input for immediate feedback (with debounce)
        let timeout;
        input.addEventListener('input', () => {
            clearTimeout(timeout);
            timeout = setTimeout(validateField, 300);
        });
    }

    validateField(group, input) {
        const rules = this.getValidationRules(input);
        const value = input.value;
        let isValid = true;
        let message = '';

        // Clear previous state
        group.classList.remove('has-error', 'has-success');
        
        for (const rule of rules) {
            const result = this.applyValidationRule(rule, value);
            if (!result.valid) {
                isValid = false;
                message = result.message;
                break;
            }
        }

        // Update UI based on validation result
        if (value.trim() !== '') { // Only show validation state if field has content
            if (isValid) {
                group.classList.add('has-success');
                this.updateFeedback(group, '✓', message);
            } else {
                group.classList.add('has-error');
                this.updateFeedback(group, '✗', message);
            }
        } else {
            this.updateFeedback(group, '', '');
        }

        return isValid;
    }

    getValidationRules(input) {
        const rules = [];
        
        // Required validation
        if (input.required) {
            rules.push({ type: 'required' });
        }
        
        // Type-based validation
        if (input.type === 'email') {
            rules.push({ type: 'email' });
        } else if (input.type === 'tel') {
            rules.push({ type: 'phone' });
        } else if (input.type === 'number') {
            rules.push({ type: 'number' });
        }
        
        // Attribute-based validation
        if (input.min) {
            rules.push({ type: 'min', value: parseFloat(input.min) });
        }
        if (input.max) {
            rules.push({ type: 'max', value: parseFloat(input.max) });
        }
        if (input.minLength) {
            rules.push({ type: 'minLength', value: parseInt(input.minLength) });
        }
        if (input.maxLength) {
            rules.push({ type: 'maxLength', value: parseInt(input.maxLength) });
        }

        return rules;
    }

    applyValidationRule(rule, value) {
        const validator = this.validationRules[rule.type];
        if (!validator) return { valid: true };

        let valid;
        if (rule.value !== undefined) {
            valid = validator(value, rule.value);
        } else {
            valid = validator(value);
        }

        let message = this.messages[rule.type] || '';
        if (rule.value !== undefined) {
            message = message.replace(`{${rule.type}}`, rule.value);
            message = message.replace('{length}', rule.value);
            message = message.replace('{min}', rule.value);
            message = message.replace('{max}', rule.value);
        }

        return { valid, message };
    }

    updateFeedback(group, icon, message) {
        const feedback = group.querySelector('.form-feedback');
        const messageEl = group.querySelector('.form-message');
        
        if (feedback) feedback.textContent = icon;
        if (messageEl) messageEl.textContent = message;
    }

    setupAutocomplete(group, input) {
        const fieldName = input.name || input.id;
        const suggestions = this.autocompleteData[fieldName];
        
        if (!suggestions) return;

        group.classList.add('autocomplete-container');
        
        const suggestionsEl = document.createElement('div');
        suggestionsEl.className = 'autocomplete-suggestions';
        group.appendChild(suggestionsEl);

        let currentIndex = -1;

        input.addEventListener('input', () => {
            const value = input.value.toLowerCase();
            const filtered = suggestions.filter(item => 
                item.toLowerCase().includes(value)
            );

            this.renderSuggestions(suggestionsEl, filtered, (suggestion) => {
                input.value = suggestion;
                suggestionsEl.classList.remove('show');
                this.validateField(group, input);
            });

            if (filtered.length > 0 && value.length > 0) {
                suggestionsEl.classList.add('show');
            } else {
                suggestionsEl.classList.remove('show');
            }
        });

        // Handle keyboard navigation
        input.addEventListener('keydown', (e) => {
            const suggestions = suggestionsEl.querySelectorAll('.autocomplete-suggestion');
            
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                currentIndex = Math.min(currentIndex + 1, suggestions.length - 1);
                this.highlightSuggestion(suggestions, currentIndex);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                currentIndex = Math.max(currentIndex - 1, -1);
                this.highlightSuggestion(suggestions, currentIndex);
            } else if (e.key === 'Enter' && currentIndex >= 0) {
                e.preventDefault();
                suggestions[currentIndex].click();
            } else if (e.key === 'Escape') {
                suggestionsEl.classList.remove('show');
                currentIndex = -1;
            }
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!group.contains(e.target)) {
                suggestionsEl.classList.remove('show');
                currentIndex = -1;
            }
        });
    }

    renderSuggestions(container, suggestions, onSelect) {
        container.innerHTML = '';
        
        suggestions.forEach(suggestion => {
            const el = document.createElement('div');
            el.className = 'autocomplete-suggestion';
            el.textContent = suggestion;
            el.addEventListener('click', () => onSelect(suggestion));
            container.appendChild(el);
        });
    }

    highlightSuggestion(suggestions, index) {
        suggestions.forEach((el, i) => {
            el.classList.toggle('highlighted', i === index);
        });
    }

    setupFocusEffects(input) {
        input.addEventListener('focus', () => {
            input.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', () => {
            input.parentElement.classList.remove('focused');
        });
    }

    addProgressIndicator(form) {
        const existingProgress = form.querySelector('.form-progress');
        if (existingProgress) return;

        const progress = document.createElement('div');
        progress.className = 'form-progress';
        progress.innerHTML = '<div class="form-progress-bar"></div>';
        
        form.insertBefore(progress, form.firstChild);
    }

    updateFormProgress(form) {
        const progressBar = form.querySelector('.form-progress-bar');
        if (!progressBar) return;

        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        const validInputs = Array.from(inputs).filter(input => {
            const group = input.closest('.form-group');
            return group && group.classList.contains('has-success');
        });

        const progress = inputs.length > 0 ? (validInputs.length / inputs.length) * 100 : 0;
        progressBar.style.width = `${progress}%`;
    }

    setupFormSubmission(form) {
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            
            // Validate all fields
            const inputs = form.querySelectorAll('input, select, textarea');
            let isFormValid = true;
            
            inputs.forEach(input => {
                const group = input.closest('.form-group');
                if (group) {
                    const isValid = this.validateField(group, input);
                    if (!isValid) isFormValid = false;
                }
            });

            if (isFormValid) {
                this.submitForm(form);
            } else {
                this.showFormError(form, 'Please correct the errors above');
            }
        });
    }

    submitForm(form) {
        // Add loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.textContent = 'Saving...';
        }

        // Simulate form submission (replace with actual submission logic)
        setTimeout(() => {
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Save Entry';
            }
            
            // Call the original form submission handler if it exists
            if (typeof submitDealershipForm === 'function') {
                submitDealershipForm();
            }
        }, 1000);
    }

    showFormError(form, message) {
        // You can implement a toast notification or form-level error display here
        console.error('Form validation failed:', message);
    }

    setupGlobalValidation() {
        // Setup any global validation rules or behaviors
        document.addEventListener('DOMContentLoaded', () => {
            this.init();
        });
    }
}

// Initialize the form enhancer
const formEnhancer = new FormEnhancer();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => formEnhancer.init());
} else {
    formEnhancer.init();
}
