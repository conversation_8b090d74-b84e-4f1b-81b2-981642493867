<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TrueBDC Dealership Performance Dashboard{% endblock %}</title>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <img src="{{ url_for('static', filename='images/logo.png') }}" alt="TrueBDC Logo" class="logo-image">
                    <span class="logo-text">TrueBDC Dealership Performance Dashboard</span>
                </div>
                <div class="header-stats">
                    <div class="stat-item">
                        <div class="stat-value" id="totalLeads">—</div>
                        <div class="stat-label">Total Leads</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="totalAppointments">—</div>
                        <div class="stat-label">Appointments</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="averageConversion">—</div>
                        <div class="stat-label">Avg Conversion</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="activeStores">—</div>
                        <div class="stat-label">Active Stores</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="nav">
        <div class="container">
            <div class="nav-content">
                <div class="nav-buttons">
                    <a href="{{ url_for('dashboard') }}" class="btn btn-primary">📊 Dashboard</a>
                    <a href="{{ url_for('data_entry') }}" class="btn btn-secondary">📝 Data Entry</a>
                    <button class="btn btn-secondary">📈 Reports</button>
                    <button class="btn btn-secondary">⚙️ Settings</button>
                </div>
                
                {% block navigation_extra %}{% endblock %}
            </div>
        </div>
    </div>

    <div class="container">
        <div class="main-content">
            {% block content %}{% endblock %}
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="{{ url_for('static', filename='js/dataManager.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
